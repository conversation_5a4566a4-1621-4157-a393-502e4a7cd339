import * as THREE from "three";
import {OtherPlayerData} from "../Character/OtherPlayer";
import createUseGame from "../../src/stores/useGame";
import AvatarData from "../../AvatarOrdinalsBrowser/renderAvatar/Avatar/Data/AvatarData";
import GlobalSpaceEvent, {GlobalDataKey} from "../Global/GlobalSpaceEvent";
import {io, Socket} from 'socket.io-client';
import {AppGameApiKey, GetMyPlayer} from "@/world/Character/MyPlayer";
import toast from "react-hot-toast";
import {toFormatAccount} from "@/utils";
import {ChatManager} from "@/model/Chat/ChatManager";
import {RedPacketManager} from "@/model/RedPacket/RedPacketManager";
import {game} from "@/world/Proto/generated/game_messages";
import ProtobufHandler from "@/world/Proto/protobuf-handler";
import ClientProtobufEncoder from "@/world/Proto/client-protobuf-encoder";
import ClientProtobufHandler from "@/world/Proto/client-protobuf-handler";

// 基础Socket事件
export enum SocketEvents {
  // 连接事件
  CONNECT = "connect",
  DISCONNECT = "disconnect",
  PLAYER_LOGIN = "player_login",

  // 房间事件
  JOIN_ROOM = "join_room",
  LEAVE_ROOM = "leave_room",
  PLAYER_ACTION = "player_action",
  PLAYER_REQUEST = "player_request",
  PLAYER_CHAT = "player_chat",
  PLAYER_HEARTBEAT = "player_heartbeat",
  PLAYER_NOTICE = "player_notice",
  WEB_NOTICE = "web_notice",
  PLAYER_KICK = "player_kick",
  MATCH_FOUND = "match_found",

  GET_ROOM_LIST = "get-room-list",

  ERROR = "error"
}


interface GameNodeInfo {
  id: string;
  mode: number;
  modeIndex: number;
  playerCount: number;
  maxPlayerCount: number;
}

export enum ClientRequestTypes {
  PickUpRedPacket = 1,
}

export enum W2C_PacketTypes {
  RedPacketReward = 1,
}

type ChatMessage = {
  playerId: string,
  content: string,
  timestamp: number
}

class GameNetWork {
  private mapId = 0
  private mapIndex = 0

  private socket: Socket | null = null
  private otherPlayers: OtherPlayerData[] = []
  private isEnterRoom = false
  private connectTime = 0
  private petPosition: THREE.Vector3 = new THREE.Vector3()
  private petQuaternion: THREE.Quaternion = new THREE.Quaternion()
  private petAnimation = ''
  private animation = ''
  private petId = ''
  private lastPing = 0
  private updatePlayersCall: (otherPlayers: OtherPlayerData[]) => void = () => {
  }

  private changeRoomCall: ((status: { isEnterRoom: boolean, mapId: number, mapIndex: number }) => void)[] = []

  constructor() {
    this.startHeartbeat()
  }

  isConnected() {
    return this.connectTime > 0
  }

  watchRoomStatus(callback: (status: { isEnterRoom: boolean, mapId: number, mapIndex: number }) => void) {
    this.changeRoomCall.push(callback)
  }

  getRoomStatus() {
    return {
      isEnterRoom: this.isEnterRoom
    }
  }

  //心跳包
  startHeartbeat() {
    setInterval(async () => {
      if (this.connectTime > 0 && this.socket) {
        try {
          const sendTime = Date.now()
          const response = await this.socket.timeout(5000).emitWithAck(SocketEvents.PLAYER_HEARTBEAT, {lastPing: this.lastPing});
          const serverTime = response as number
          if (serverTime) {
            const respTime = Date.now()
            this.lastPing = (respTime) - (sendTime)
          }
        } catch (err) {
          console.error('ping error', err)
        }
      }
      this.checkPlayerDistance()
    }, 1000)
  }

  getPlayerList() {
    const btcAddressList = []
    for (let i = 0; i < this.otherPlayers.length; i++) {
      const player = this.otherPlayers[i]
      if (player.name.length > 0 && player.id.length > 0) {
        btcAddressList.push(player.id)
      }
    }
    return btcAddressList
  }

  findOtherPlayer(id: string) {
    return this.otherPlayers.find((player) => player.id === id)
  }

  deleteOtherPlayer(id: string) {
    for (let i = 0; i < this.otherPlayers.length; i++) {
      const player = this.otherPlayers[i]
      if (player.id === id) {
        player.id = ''
        break
      }
    }
  }

  addOtherPlayer(btcAddress: string, avatarData: AvatarData, usePet: string = '') {
    // console.log('addOtherPlayer', id, avatarData, usePet)
    const useGame = createUseGame()
    const otherPlayer = new OtherPlayerData(btcAddress, avatarData, useGame)
    otherPlayer.usePet = usePet
    otherPlayer.meshScale = 1
    if (!btcAddress.includes('pet')) {
      otherPlayer.name = toFormatAccount(btcAddress)
    }
    this.otherPlayers.push(otherPlayer)
    if (this.isEnterRoom) {
      this.updatePlayersCall(this.otherPlayers)
    }
    return otherPlayer
  }

  checkPlayerDistance() {
    if (this.otherPlayers.length === 0)
      return

    const list: { distance: number, player: OtherPlayerData }[] = []
    const myPlayer = GetMyPlayer()
    this.otherPlayers.forEach((player) => {
      if (player.id.length > 0) {
        const distance = myPlayer.position.distanceTo(player.position)
        list.push({distance, player})
      }
    })
    list.sort((a, b) => {
      return a.distance - b.distance
    })

    list.forEach((item, index) => {
      item.player.isVisible = index < 10
    })
  }

  clearDeadPlayer() {
    const list = []
    for (let i = 0; i < this.otherPlayers.length; i++) {
      const player = this.otherPlayers[i]
      if (player.id.length > 0) {
        list.push(player)
      }
    }
    this.otherPlayers = list
    if (this.isEnterRoom) {
      this.updatePlayersCall(this.otherPlayers)
    }
  }

  getDeadPlayerCount() {
    let count = 0
    for (let i = 0; i < this.otherPlayers.length; i++) {
      const player = this.otherPlayers[i]
      if (player.id === '') {
        count++
      }
    }
    return count
  }

  connect(socket_url: string) {
    // console.log('Connecting to server...', host, port);
    if (this.socket) {
      // console.log('Already connected to the server');
      return;
    }
    const myPlayer = GetMyPlayer()
    const _btcAddress = myPlayer.btcAddress || ''
    const _sessionId = myPlayer.sessionId || ''
    if (_btcAddress.length === 0) {
      console.log('Please input btc address first');
      return;
    }
    // 使用传入的host和port参数
    let socket = io(socket_url, {
      withCredentials: false,
      transports: ['websocket', 'polling'],
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000
    });
    this.socket = socket

    // 连接事件处理
    socket.on(SocketEvents.CONNECT, async () => {
      // console.log('Connected to the server');
      this.connectTime = 0
      const success: boolean = await socket.timeout(30 * 1000).emitWithAck(SocketEvents.PLAYER_LOGIN, {
        btcAddress: _btcAddress,
        sessionId: _sessionId,
      });
      if (success) {
        this.connectTime = Date.now()
      }
    });

    socket.on(SocketEvents.DISCONNECT, () => {
      // console.log('Disconnected from gateway server', (Date.now() - this.connectTime) / 1000);
      this.connectTime = 0
      this.isEnterRoom = false
      this.changeRoomCall.forEach((callback) => {
        callback({isEnterRoom: false, mapId: 0, mapIndex: 0})
      })
    });

    socket.on(SocketEvents.PLAYER_KICK, () => {
      toast.error('Kick from gateway server');
      myPlayer.callAppApi(AppGameApiKey.disconnectWallet)
    });

    socket.on(SocketEvents.WEB_NOTICE, (data) => {
      this.webNotice(data)
    });

    socket.on(SocketEvents.PLAYER_NOTICE, (binaryData: Buffer) => {
      console.log('PLAYER_NOTICE', binaryData)
      const {pid, data} = ProtobufHandler.getInstance().decodeMessage(binaryData);
      // const player = this.findOtherPlayer(btcAddress);
      switch (pid) {
        case game.S2CPacketType.S2C_PLAYER_ENTER:
          const enterData = data as game.PlayerEnter
          // 有新玩家连接时的处理
          // console.log('Player connected:', btcAddress);

          if (!this.findOtherPlayer(enterData.btcAddress)) {
            const avatarData = new AvatarData()
            avatarData.shirtId = enterData.avatarData?.shirtId || null
            avatarData.shirtTextureId = enterData.avatarData?.shirtTextureId || null
            avatarData.shirtColor = enterData.avatarData?.shirtColor || null
            avatarData.pantsId = enterData.avatarData?.pantsId || null
            avatarData.shoesId = enterData.avatarData?.shoesId || null
            avatarData.hatId = enterData.avatarData?.hatId || null
            const otherPlayer = this.addOtherPlayer(enterData.btcAddress, avatarData);
            otherPlayer.position.set(enterData.position?.x || 0, enterData.position?.y || 0, enterData.position?.z || 0);
            otherPlayer.quaternion.set(enterData.position?.rotationX || 0, enterData.position?.rotationY || 0, enterData.position?.rotationZ || 0, enterData.position?.rotationW || 0);
            // otherPlayer.curAnimation = enterData.curAnimation;
            // otherPlayer.itemId = enterData.itemId;
            // otherPlayer.pizzaCount = enterData.pizzaCount;
            otherPlayer.isChange = true
          }
          break;
        case game.S2CPacketType.S2C_PLAYER_POSITION:
          // 更新其他玩家的位置和方向
          const positionData = data as game.PlayerPosition
          const positionPlayer = this.findOtherPlayer(positionData.btcAddress);
          if (positionPlayer) {
            positionPlayer.position.set(positionData?.x || 0, positionData?.y || 0, positionData?.z || 0);
            positionPlayer.quaternion.set(positionData?.rotationX || 0, positionData?.rotationY || 0, positionData?.rotationZ || 0, positionData?.rotationW || 0);
            positionPlayer.timestamp = Date.now();
            positionPlayer.isChange = true
          }
          break;
        case game.S2CPacketType.S2C_PLAYER_ANIMATION:
          // console.log('Player update_animation:', data);
          const animationData = data as game.PlayerAnimation
          const animationPlayer = this.findOtherPlayer(animationData.btcAddress);
          // 更新其他玩家的动作
          if (animationPlayer) {
            animationPlayer.curAnimation = animationData.curAnimation;
            animationPlayer.isChange = true
          }
          break;
        case game.S2CPacketType.S2C_PLAYER_UPDATE:
          const updateData = data as game.PlayerUpdate
          const updatePlayer = this.findOtherPlayer(updateData.btcAddress);
          // console.log('Player update_item:', data);
          // 更新其他玩家的动作
          const {itemId, pizzaCount, petId} = updateData
          if (updatePlayer) {
            if (updatePlayer.itemId !== itemId) {
              updatePlayer.itemId = itemId;
              updatePlayer.isChange = true
            }
            if (updatePlayer.pizzaCount !== pizzaCount) {
              updatePlayer.pizzaCount = pizzaCount;
              updatePlayer.isChange = true
            }
            const pet_1 = this.findOtherPlayer(`${data.btcAddress}_pet`);
            if (petId) {
              if (pet_1) {
                if (pet_1.usePet !== petId) {
                  pet_1.usePet = petId
                  pet_1.isChange = true
                }
              } else {
                this.addOtherPlayer(`${data.btcAddress}_pet`, new AvatarData(), petId)
              }
            } else {
              this.deleteOtherPlayer(`${data.btcAddress}_pet`)
            }
          }
          break;
        case game.S2CPacketType.S2C_PET_POSITION:
          const petPositionData = data as game.PetPosition
          // console.log('Pet update_position:' + data.btcAddress, data);
          // 更新其他玩家的位置和方向
          const pet_2 = this.findOtherPlayer(`${petPositionData.ownerBtcAddress}_pet`);
          if (pet_2) {
            pet_2.position.set(petPositionData?.x || 0, petPositionData?.y || 0, petPositionData?.z || 0);
            pet_2.quaternion.set(petPositionData?.rotationX || 0, petPositionData?.rotationY || 0, petPositionData?.rotationZ || 0, petPositionData?.rotationW || 0);
            pet_2.timestamp = Date.now();
            pet_2.isChange = true
          }
          break;
        case game.S2CPacketType.S2C_PET_ANIMATION:
          // console.log('Pet update_animation:' + data.btcAddress, data);
          // 更新其他玩家的动作
          const pet_3 = this.findOtherPlayer(`${data.btcAddress}_pet`);
          if (pet_3) {
            pet_3.curAnimation = data.curAnimation;
            pet_3.isChange = true
          }
          break;
        case game.S2CPacketType.S2C_PLAYER_LEAVE:
          const leaveData = data as game.PlayerLeave
          const leavePlayer = this.findOtherPlayer(leaveData.btcAddress);
          if (leavePlayer) {
            this.deleteOtherPlayer(leaveData.btcAddress)
            this.deleteOtherPlayer(`${leaveData.btcAddress}_pet`)
          }
          break;
        case game.S2CPacketType.S2C_CHAT_ENTER:
          ChatManager.getInstance().enterChatType(data.chatId)
          break;
        case game.S2CPacketType.S2C_CHAT_MESSAGE:
          const {tabType} = data
          ChatManager.getInstance().addChatMessage(tabType, data)
          break;
        case game.S2CPacketType.S2C_CHAT_LEAVE:
          ChatManager.getInstance().outChatType(data.chatId)
          break;
        case game.S2CPacketType.S2C_RED_PACKET_REWARD:
          const {redPacketList} = data
          RedPacketManager.getInstance().receiveRedPacket(redPacketList)
          break;
      }
    });

    socket.on(SocketEvents.MATCH_FOUND, (data) => {
      try {
        const {mode, modeIndex} = data
        this.mapId = mode.split('_')[1] as unknown as number
        this.mapIndex = modeIndex as number || 0
        this.otherPlayers = [];
        this.changeRoomCall.forEach((callback) => {
          callback({isEnterRoom: true, mapId: this.mapId, mapIndex: this.mapIndex})
        })
        this.updatePlayersCall(this.otherPlayers)

        GlobalSpaceEvent.ListenKeyDataChange<AvatarData>(GlobalDataKey.MyAvatarData, async (avatarData) => {
          socket.emit(SocketEvents.PLAYER_ACTION, {
            pid: game.C2SPacketType.C2S_PLAYER_ENTER,
            avatarData,
            itemId: myPlayer.itemData?.id || 0,
            pizzaCount: myPlayer.pizzaCount,
            petId: this.petId,
            animation: this.animation,
            position: {
              x: Math.floor(myPlayer.position.x * 100) / 100,
              y: Math.floor(myPlayer.position.y * 100) / 100,
              z: Math.floor(myPlayer.position.z * 100) / 100,
            },
            quaternion: {
              x: Math.floor(myPlayer.quaternion.x * 100) / 100,
              y: Math.floor(myPlayer.quaternion.y * 100) / 100,
              z: Math.floor(myPlayer.quaternion.z * 100) / 100,
              w: Math.floor(myPlayer.quaternion.w * 100) / 100,
            }
          });
          this.sendPetAnimation(this.petAnimation)
          this.sendPetPosition(this.petPosition, this.petQuaternion)
          this.isEnterRoom = true
        }, true)
      } catch (e) {
        toast.error('enter room error');
        console.log('MATCH_FOUND error', e)
        this.leaveRoom()
      }
    });
    socket.on(SocketEvents.LEAVE_ROOM, (data) => {
      try {
        this.leaveRoom()
      } catch (e) {
      }
    });
    socket.on(SocketEvents.ERROR, (message) => {
      toast.error(message);
    });
  }

  webNotice(notice: any) {
    console.log('webNotice', notice)
    const {pid, data} = notice
    switch (pid) {
      case W2C_PacketTypes.RedPacketReward:
        const {tick, amount} = data
        const myPlayer = GetMyPlayer()
        console.log('showRewards', tick, amount)
        myPlayer.callAppApi(AppGameApiKey.showRewards, tick, amount);
        break;
    }
  }

  disConnect() {
    if (this.socket && this.socket.active) {
      this.socket.close();
    }
    this.isEnterRoom = false
    this.changeRoomCall.forEach((callback) => {
      callback({isEnterRoom: false, mapId: 0, mapIndex: 0})
    })
    this.connectTime = 0
    this.socket = null;
    this.otherPlayers = [];
    this.updatePlayersCall(this.otherPlayers);
  }

  sendPosition(position: THREE.Vector3, quaternion: THREE.Quaternion) {
    if (!this.isEnterRoom) {
      return
    }
    // 如果 WebSocket 连接是打开的，发送位置和旋转数据到服务器z
    const socket = this.socket
    if (socket && socket.active) {

      socket.emit(SocketEvents.PLAYER_ACTION, ClientProtobufEncoder.getInstance().encodePlayerAction(
        game.C2SPacketType.C2S_PLAYER_POSITION,
        game.ClientPlayerPosition.create({
          x: Math.floor(position.x * 10) / 10,
          y: Math.floor(position.y * 10) / 10,
          z: Math.floor(position.z * 10) / 10,
          rotationX: Math.floor(quaternion.x * 100) / 100,
          rotationY: Math.floor(quaternion.y * 100) / 100,
          rotationZ: Math.floor(quaternion.z * 100) / 100,
          rotationW: Math.floor(quaternion.w * 100) / 100,
        }).toJSON()
      ));
    }
  }

  sendAnimation(animation: string) {
    this.animation = animation
    // 如果 WebSocket 连接是打开的，发送位置和旋转数据到服务器
    if (!this.isEnterRoom) {
      return
    }
    const socket = this.socket
    if (socket && socket.active) {
      socket.emit(SocketEvents.PLAYER_ACTION, ClientProtobufEncoder.getInstance().encodePlayerAction(
        game.C2SPacketType.C2S_PLAYER_ANIMATION,
        game.ClientPlayerAnimation.create({
          curAnimation: animation
        }).toJSON()
      ));
    }
  }

  sendPetPosition(position: THREE.Vector3, quaternion: THREE.Quaternion) {
    this.petPosition = position
    this.petQuaternion = quaternion
    if (!this.isEnterRoom) {
      return
    }
    const socket = this.socket
    if (socket && socket.active) {
      socket.emit(SocketEvents.PLAYER_ACTION, ClientProtobufEncoder.getInstance().encodePlayerAction(
        game.C2SPacketType.C2S_PET_POSITION,
        game.ClientPlayerPosition.create({
          x: Math.floor(position.x * 100) / 100,
          y: Math.floor(position.y * 100) / 100,
          z: Math.floor(position.z * 100) / 100,
          rotationX: Math.floor(quaternion.x * 100) / 100,
          rotationY: Math.floor(quaternion.y * 100) / 100,
          rotationZ: Math.floor(quaternion.z * 100) / 100,
          rotationW: Math.floor(quaternion.w * 100) / 100,
        }).toJSON()
      ));
    }
  }

  sendPetAnimation(animation: string) {
    this.petAnimation = animation
    // 如果 WebSocket 连接是打开的，发送位置和旋转数据到服务器
    if (!this.isEnterRoom) {
      return
    }
    const socket = this.socket
    if (socket && socket.active) {
      socket.emit(SocketEvents.PLAYER_ACTION, ClientProtobufEncoder.getInstance().encodePlayerAction(
        game.C2SPacketType.C2S_PET_ANIMATION,
        game.ClientPlayerAnimation.create({
          curAnimation: animation
        }).toJSON()
      ));
    }
  }

  sendItemId() {
    if (!this.isEnterRoom) {
      return
    }
    const myPlayer = GetMyPlayer()
    const itemId = myPlayer.itemData?.id || 0
    const pizzaCount = myPlayer.pizzaCount
    const socket = this.socket
    if (socket && socket.active) {
      socket.emit(SocketEvents.PLAYER_ACTION, ClientProtobufEncoder.getInstance().encodePlayerAction(
        game.C2SPacketType.C2S_PLAYER_UPDATE,
        game.ClientPlayerUpdate.create({
          itemId,
          pizzaCount,
          petId: this.petId
        }).toJSON()
      ));
    }
  }

  sendPizzaCount() {
    const myPlayer = GetMyPlayer()
    const itemId = myPlayer.itemData?.id || 0
    const pizzaCount = myPlayer.pizzaCount
    const socket = this.socket
    if (socket && socket.active) {
      socket.emit(SocketEvents.PLAYER_ACTION, ClientProtobufEncoder.getInstance().encodePlayerAction(
        game.C2SPacketType.C2S_PLAYER_UPDATE,
        game.ClientPlayerUpdate.create({
          itemId,
          pizzaCount,
          petId: this.petId
        }).toJSON()
      ));
    }
  }

  sendPetId(petId: string) {
    this.petId = petId
    if (!this.isEnterRoom) {
      return
    }
    const myPlayer = GetMyPlayer()
    const pizzaCount = myPlayer.pizzaCount
    const itemId = myPlayer.itemData?.id || 0
    const socket = this.socket
    if (socket && socket.active) {
      socket.emit(SocketEvents.PLAYER_ACTION, ClientProtobufEncoder.getInstance().encodePlayerAction(
        game.C2SPacketType.C2S_PLAYER_UPDATE,
        game.ClientPlayerUpdate.create({
          itemId,
          pizzaCount,
          petId,
        })
      ));
    }
  }

  sendChatMsg(chatId: number, pid: game.C2SPacketType, data: game.ChatMessage) {
    const socket = this.socket
    if (socket && socket.active) {
      const binaryData = ClientProtobufEncoder.getInstance().encodePlayerChat(
        chatId,
        pid,
        data.toJSON()
      )
      socket.emit(SocketEvents.PLAYER_CHAT, binaryData);
      // this.socket.emit(SocketEvents.PLAYER_NOTICE, protobufData);
      console.log('sendChatMsg binary', binaryData)
      console.log('sendChatMsg json', ClientProtobufHandler.getInstance().decodePlayerChat(binaryData))
    }
  }

  updatePlayers(call: (otherPlayers: OtherPlayerData[]) => void) {
    this.updatePlayersCall = call;
  }

  async getRoomList() {
    const socket = this.socket
    const mapId = this.mapId
    if (socket && socket.active) {
      const response: GameNodeInfo[] = await socket.timeout(10000).emitWithAck(SocketEvents.GET_ROOM_LIST, {mapId});
      return response
    }
    return []
  }

  async request(pid: ClientRequestTypes, body: any) {
    const socket = this.socket
    if (socket && socket.active) {
      return socket.emitWithAck(SocketEvents.PLAYER_REQUEST, {
        body,
        pid,
      });
    }
    return null
  }

  enterRoom(mapId: number, index: number) {
    if (!this.socket) {
      return
    }
    const socket = this.socket
    const myPlayer = GetMyPlayer()
    GlobalSpaceEvent.ListenKeyDataChange<AvatarData>(GlobalDataKey.MyAvatarData, (avatarData) => {
      socket.emit(SocketEvents.JOIN_ROOM, {
        btcAddress: myPlayer.btcAddress,
        avatarData: avatarData,
        mapId: mapId,
        mapIndex: index
      });
    }, true)
  }

  leaveRoom() {
    if (!this.socket) {
      return
    }
    if (!this.isEnterRoom) {
      return
    }
    this.mapId = 0
    this.mapIndex = 0
    const socket = this.socket
    this.isEnterRoom = false
    this.changeRoomCall.forEach((callback) => {
      callback({isEnterRoom: false, mapId: 0, mapIndex: 0})
    })
    this.otherPlayers = [];
    this.updatePlayersCall(this.otherPlayers);
    const myPlayer = GetMyPlayer()
    socket.emit(SocketEvents.LEAVE_ROOM, {
      btcAddress: myPlayer.btcAddress
    });
  }
}

let gameNetWork: GameNetWork | null = null

export function GetGameNetWork() {
  if (gameNetWork == null) {
    gameNetWork = new GameNetWork()
  }
  return gameNetWork
}
