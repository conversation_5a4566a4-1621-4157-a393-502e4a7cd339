/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three'
import React, {useEffect, useRef} from 'react'
import GlobalSpaceEvent, {GlobalDataKey} from "../../Global/GlobalSpaceEvent";

import {GLTFLoader} from "three/examples/jsm/loaders/GLTFLoader";
import {GLTF} from "three-stdlib";
import {RigidBody} from "@react-three/rapier";
import Island_01 from "../Island/Island_01";
import OnlineWorld_01 from "./OnlineWorld_01";
import {getCdnLink} from "../../../AvatarOrdinalsBrowser/utils";

export default function OnlineWorld({}: {}) {
    const group = useRef<THREE.Group>()
    // const {nodes, materials, animations} = useGLTF('./space/glb/Island_00.glb') as GLTFResult
    const [island_01, setIsland_01] = React.useState<GLTF | null>(null)

    useEffect(() => {
        if (group.current) {
            const loader = new GLTFLoader();
            loader.load(getCdnLink('./space/glb/OnlineWorld.glb'), function (gltf) {
                setIsland_01(gltf as any);
                const timer = setTimeout(() => {
                    GlobalSpaceEvent.SetDataValue(GlobalDataKey.SceneLoading, false)
                }, 1000)
            })
        }

    }, [group.current])

    return (
        <group ref={group} userData={{camCollisionListener: true}}>
            {
                island_01 &&
                <RigidBody type="fixed" colliders="trimesh" ccd>
                    <OnlineWorld_01 gltf={island_01}/>
                </RigidBody>
            }
        </group>
    )
}

// useGLTF.preload('./space/glb/Island.glb')
// useTexture.preload('./space/textures/sky.png')
