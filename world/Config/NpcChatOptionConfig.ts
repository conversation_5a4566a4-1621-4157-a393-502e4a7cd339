import {ConfigManager} from "@/world/Config/ConfigManager";

export enum NpcChatOptionType {
  Twitter = 1,
  ReceiveTool,
  BuyTool,
  ActivityRule,
  SubmitResources,
  ShareTwitter,
  Donation,
  ClaimDrop,
  OpenSynthesis,
  JoinActivity,
  JumpLink,
  RoomList = 997,
  EnterRoom = 998,
  LeaveRoom = 999,
  JumpChat,//跳转选项
}

export type NpcChatOptionData = {
  id: number;
  text: string;
  type: number;
  params: string[];
  clickDelay: number;
  iconUrl: string;
}

export class NpcChatOptionConfig {
  private static instance: NpcChatOptionConfig

  private npcChatOptionDataMap: Map<number, { url: string, data: NpcChatOptionData | undefined }>

  private constructor() {
    this.npcChatOptionDataMap = new Map<number, { url: string, data: NpcChatOptionData | undefined }>()
    ConfigManager.getInstance().downloadConfig('./config/npcChatOption/_ids.json', (data: {
      id: number,
      url: string
    }[]) => {
      for (let i = 0; i < data.length; i++) {
        const config = data[i]
        this.npcChatOptionDataMap.set(config.id, {url: config.url, data: undefined})
      }
    })
  }

  static getInstance() {
    if (!NpcChatOptionConfig.instance) {
      NpcChatOptionConfig.instance = new NpcChatOptionConfig()
    }
    return NpcChatOptionConfig.instance
  }

  private loadMapData(config: {
    url: string,
    data: NpcChatOptionData | undefined
  }, cb: (data: NpcChatOptionData) => void) {
    ConfigManager.getInstance().downloadConfig(config.url, (data) => {
      const npcChatOptionData = data as NpcChatOptionData
      npcChatOptionData.text = npcChatOptionData.text || ''
      npcChatOptionData.type = npcChatOptionData.type || NpcChatOptionType.Twitter
      npcChatOptionData.params = npcChatOptionData.params || []
      npcChatOptionData.clickDelay = npcChatOptionData.clickDelay || 1000
      npcChatOptionData.iconUrl = npcChatOptionData.iconUrl || ''
      config.data = npcChatOptionData
      cb(npcChatOptionData)
    })
  }

  getData(id: number, cb: (data: NpcChatOptionData) => void) {
    if (this.npcChatOptionDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb)
      }, 500)
      return
    }

    const config = this.npcChatOptionDataMap.get(id)
    if (config) {
      const data = config.data
      if (data) {
        cb(data)
      } else {
        this.loadMapData(config, cb)
      }
    } else {
      console.error('not found npc chat option config id: ' + id)
    }
  }

  getDataList(idList: number[], cb: (data: NpcChatOptionData[]) => void) {
    const dataMap = new Map<number, NpcChatOptionData>()
    idList.forEach((id) => {
      this.getData(id, (data) => {
        dataMap.set(id, data)
        if (dataMap.size === idList.length) {
          const dataList: NpcChatOptionData[] = []
          idList.forEach((id) => {
            dataList.push(dataMap.get(id) as NpcChatOptionData)
          })
          cb(dataList)
        }
      })
    })
  }

}