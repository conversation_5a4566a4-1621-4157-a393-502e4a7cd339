import {ConfigManager} from "@/world/Config/ConfigManager";

export type MapData = {
  id: number
  name: string;
  mainGlb: string;
  glbList: string[];
  doorIds: number[];
  npcIds: number[];
  particleIds: number[];
  treeIds: number[];
  stoneIds: number[];
  fishingAreaIds: number[];
  itemDropIds: number[];
  bgmUrl: string;
  offset: number[]
}

export class MapConfig {
  private static instance: MapConfig

  private mapDataMap: Map<number, { url: string, data: MapData }>
  private curMapData: MapData | null = null

  private constructor() {
    this.mapDataMap = new Map<number, { url: string, data: MapData }>()
    ConfigManager.getInstance().downloadConfig('./config/map/_totals.json', (data: MapData[]) => {
      for (let i = 0; i < data.length; i++) {
        const mapData = data[i]
        mapData.name = mapData.name || ''
        mapData.mainGlb = mapData.mainGlb || ''
        mapData.bgmUrl = mapData.bgmUrl || ''
        mapData.glbList = mapData.glbList || []
        mapData.doorIds = mapData.doorIds || []
        mapData.npcIds = mapData.npcIds || []
        mapData.particleIds = mapData.particleIds || []
        mapData.treeIds = mapData.treeIds || []
        mapData.stoneIds = mapData.stoneIds || []
        mapData.fishingAreaIds = mapData.fishingAreaIds || []
        mapData.itemDropIds = mapData.itemDropIds || []
        this.mapDataMap.set(mapData.id, {url: '', data: mapData})
      }
    })
  }

  static getInstance() {
    if (!MapConfig.instance) {
      MapConfig.instance = new MapConfig()
    }
    return MapConfig.instance
  }

  setCurMapData(mapData: MapData | null) {
    this.curMapData = mapData
  }

  getCurMapData() {
    return this.curMapData
  }

  getMapData(id: number, cb: (data: MapData) => void) {
    if (this.mapDataMap.size === 0) {
      setTimeout(() => {
        this.getMapData(id, cb)
      }, 500)
      return
    }

    const config = this.mapDataMap.get(id)
    if (config) {
      cb(config.data)
    } else {
      console.error('not found map config id: ' + id)
    }
  }
}