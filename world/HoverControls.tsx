import {forwardRef, useImperativeHandle, useRef} from "react";
import * as THREE from "three";
import {useThree} from "@react-three/fiber";
import GlobalSpace from "./Global/GlobalSpace";
import {Raycaster} from "three/src/Three.Core";

const HoverControls = forwardRef((props: any, ref: any) => {
    const raycaster = useRef(new Raycaster());
    const mouse = useRef(new THREE.Vector2());
    const {camera} = useThree();

    // 给父级调用
    useImperativeHandle(ref, () => ({
        // 根据key更新metadata
        handleMouseMove: (event: any) => {
            // 更新鼠标位置 (归一化)
            mouse.current.x = (event.clientX / window.innerWidth) * 2 - 1;
            mouse.current.y = -(event.clientY / window.innerHeight) * 2 + 1;

            // 使用 Raycaster 检测物体
            raycaster.current.setFromCamera(mouse.current, camera);
            const hoverList = GlobalSpace.getHoverObjectList();
            let oldHoverObj: THREE.Object3D | null = null;
            for (let i = 0; i < hoverList.length; i++) {
                if (hoverList[i].userData.isHover) {
                    oldHoverObj = hoverList[i];
                    break;
                }
            }
            const intersects = raycaster.current.intersectObjects(hoverList);

            if (intersects.length > 0) {
                const hoverObj = intersects[0].object;
                if (oldHoverObj !== hoverObj) {
                    if (oldHoverObj) {
                        oldHoverObj.userData.isHover = false;
                        if (oldHoverObj.userData.unHoverCallback) {
                            oldHoverObj.userData.unHoverCallback()
                        }
                    }
                    hoverObj.userData.isHover = true;
                    if (hoverObj.userData.hoverCallback) {
                        hoverObj.userData.hoverCallback()
                    }
                }
            } else {
                if (oldHoverObj) {
                    oldHoverObj.userData.isHover = false;
                    if (oldHoverObj.userData.unHoverCallback) {
                        oldHoverObj.userData.unHoverCallback()
                    }
                }
            }
        },
        handleMouseLeave: (event: any) => {
            const hoverList = GlobalSpace.getHoverObjectList();
            let oldHoverObj: THREE.Object3D | null = null;
            for (let i = 0; i < hoverList.length; i++) {
                if (hoverList[i].userData.isHover) {
                    oldHoverObj = hoverList[i];
                    break;
                }
            }
            if (oldHoverObj) {
                oldHoverObj.userData.isHover = false;
                if (oldHoverObj.userData.unHoverCallback) {
                    oldHoverObj.userData.unHoverCallback()
                }
            }
        },
        handleMouseUp: (event: any) => {

            // 更新鼠标位置 (归一化)
            mouse.current.x = (event.clientX / window.innerWidth) * 2 - 1;
            mouse.current.y = -(event.clientY / window.innerHeight) * 2 + 1;

            // 使用 Raycaster 检测物体
            raycaster.current.setFromCamera(mouse.current, camera);

            const clickList = GlobalSpace.getClickObjectList();
            const intersects = raycaster.current.intersectObjects(clickList);
            if (intersects.length > 0) {
                const clickObj = intersects[0].object;
                if (clickObj.userData.clickCallback) {
                    clickObj.userData.clickCallback()
                }
            }

        }
    }));
    return null;
})
export default HoverControls