/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import React, {useEffect, useState} from 'react'
import {OtherPlayerData} from "./OtherPlayer";
import {GetGameNetWork} from "@/world/hooks/useNetWork";
import * as THREE from "three";
import {POPOVER_HEIGHT} from "@/constant";
import {useFrame} from "@react-three/fiber";

function OtherPlayerRoot({otherPlayer}: { otherPlayer: OtherPlayerData }) {
  const groupRef = React.useRef<THREE.Group>(null)
  const [connect, setConnect] = useState<boolean>(false)
  const updateHeight = () => {
    if (groupRef.current) {
      let box = new THREE.Box3().setFromObject(groupRef.current);
      return box.max.y - box.min.y + POPOVER_HEIGHT
    }
    return 0
  }
  useFrame(() => {
    setConnect(otherPlayer.id.length > 0)
  })
  return (
    <group ref={groupRef}>
      {
        connect && otherPlayer.getReact(updateHeight)
      }
    </group>
  );
}

export default function PlayerManager() {
  const gameNetWork = GetGameNetWork()

  const [otherPlayers, setOtherPlayers] = useState<OtherPlayerData[]>([])
  useEffect(() => {
    gameNetWork.updatePlayers((otherPlayers) => {
      const newPlayer = []
      newPlayer.push(...otherPlayers)
      setOtherPlayers(newPlayer)
    })
    //todo 存在内存泄露，暂时无法解决
    //
    // setInterval(() => {
    //   if (gameNetWork.getDeadPlayerCount() > 0) {
    //     setOtherPlayers([])
    //     console.log('reset')
    //     console.log('reset')
    //     console.log('reset')
    //     console.log('reset')
    //     console.log('reset')
    //     setTimeout(() => {
    //       gameNetWork.clearDeadPlayer()
    //     }, 5000)
    //   }
    // }, 5 * 1000)
  }, []);
  return (
    <>
      {
        otherPlayers.map((player, index) => {
          return <OtherPlayerRoot otherPlayer={player}/>
        })
      }
    </>
  );
}

