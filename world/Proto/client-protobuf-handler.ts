import {game} from './generated/game_messages';

// import {ClientRequestTypes} from 'common/types';

/**
 * 客户端Protobuf消息处理器
 * 负责解析来自客户端的protobuf数据
 */
export class ClientProtobufHandler {
  private static instance: ClientProtobufHandler;

  // C2S消息类型映射：从protobuf枚举到protobuf消息类型
  static readonly c2sMessageMap = new Map<game.C2SPacketType, any>([
    [game.C2SPacketType.C2S_PLAYER_ENTER, game.ClientPlayerEnter],
    [game.C2SPacketType.C2S_PLAYER_LEAVE, null], // 无需额外数据
    [game.C2SPacketType.C2S_PLAYER_POSITION, game.ClientPlayerPosition],
    [game.C2SPacketType.C2S_PLAYER_ANIMATION, game.ClientPlayerAnimation],
    [game.C2SPacketType.C2S_PLAYER_UPDATE, game.ClientPlayerUpdate],
    [game.C2SPacketType.C2S_PET_POSITION, game.ClientPlayerPosition], // 复用位置数据结构
    [game.C2SPacketType.C2S_PET_ANIMATION, game.ClientPlayerAnimation], // 复用动画数据结构
    [game.C2SPacketType.C2S_CHAT_ENTER, null], // 无需额外数据
    [game.C2SPacketType.C2S_CHAT_LEAVE, null], // 无需额外数据
    [game.C2SPacketType.C2S_CHAT_MESSAGE, game.ChatMessage],
  ]);

  // // 客户端请求类型映射
  // private readonly clientRequestMap = new Map<ClientRequestTypes, any>([
  //   [ClientRequestTypes.PickUpRedPacket, null], // 使用通用数据结构
  // ]);

  public static getInstance(): ClientProtobufHandler {
    if (!ClientProtobufHandler.instance) {
      ClientProtobufHandler.instance = new ClientProtobufHandler();
    }
    return ClientProtobufHandler.instance;
  }

  /**
   * 解析客户端动作消息
   * @param buffer protobuf二进制数据
   * @returns 解析后的动作数据
   */
  public decodePlayerAction(buffer: Buffer): { pid: game.C2SPacketType; data: any } {
    try {
      // 解码动作消息包装器
      const actionMessage = game.PlayerActionMessage.decode(buffer);
      console.log('chatMessage', actionMessage.toJSON())
      const pid = actionMessage.pid as game.C2SPacketType;

      // 获取对应的消息类型
      const MessageClass = ClientProtobufHandler.c2sMessageMap.get(pid);

      let data: any = {};

      if (MessageClass && actionMessage.actionData && actionMessage.actionData.length > 0) {
        // 解码内部数据
        const innerMessage = MessageClass.decode(actionMessage.actionData);
        data = innerMessage.toJSON();
      }

      return {
        pid,
        data
      };
    } catch (error) {
      console.error('Error decoding player action:', error);
      throw error;
    }
  }

  //
  // /**
  //  * 解析客户端请求消息
  //  * @param buffer protobuf二进制数据
  //  * @returns 解析后的请求数据
  //  */
  // public decodePlayerRequest(buffer: Buffer): { pid: ClientRequestTypes; data: any } {
  //   try {
  //     // 解码请求消息包装器
  //     const requestMessage = game.PlayerRequestMessage.decode(buffer);
  //
  //     const pid = requestMessage.pid as ClientRequestTypes;
  //
  //     // 对于请求消息，通常使用通用的JSON数据结构
  //     let data: any = {};
  //
  //     if (requestMessage.requestData && requestMessage.requestData.length > 0) {
  //       // 尝试解析为JSON字符串
  //       try {
  //         const jsonString = Buffer.from(requestMessage.requestData).toString('utf8');
  //         data = JSON.parse(jsonString);
  //       } catch (jsonError) {
  //         console.warn('Failed to parse request data as JSON, using raw data');
  //         data = {rawData: requestMessage.requestData};
  //       }
  //     }
  //
  //     return {
  //       pid,
  //       data
  //     };
  //   } catch (error) {
  //     console.error('Error decoding player request:', error);
  //     throw error;
  //   }
  // }

  /**
   * 解析客户端聊天消息
   * @param buffer protobuf二进制数据
   * @returns 解析后的聊天数据
   */
  public decodePlayerChat(buffer: Buffer): { chatId: number; pid: game.C2SPacketType; data: any } {
    try {
      // 解码聊天消息包装器
      const chatMessage = game.PlayerChatMessage.decode(buffer);
      console.log('chatMessage', chatMessage.toJSON())
      const chatId = chatMessage.chatId;
      const pid = chatMessage.pid as game.C2SPacketType;

      let data: any = {};

      if (chatMessage.chatData && chatMessage.chatData.length > 0) {
        // 聊天消息通常是ChatMessage格式
        if (pid === game.C2SPacketType.C2S_CHAT_MESSAGE) {
          // 解码内部聊天数据
          const chatData = Buffer.from(chatMessage.chatData);
          const innerMessage = game.ChatMessage.decode(chatData);
          data = innerMessage.toJSON();
        }
      }

      return {
        chatId,
        pid,
        data
      };
    } catch (error) {
      console.error('Error decoding player chat:', error);
      throw error;
    }
  }

  /**
   * 检查是否支持指定的消息类型
   */
  public isActionSupported(packetType: game.C2SPacketType): boolean {
    return ClientProtobufHandler.c2sMessageMap.has(packetType);
  }

  /**
   * 检查是否支持指定的请求类型
   */
  // public isRequestSupported(requestType: ClientRequestTypes): boolean {
  //   return this.clientRequestMap.has(requestType);
  // }

  /**
   * 获取支持的动作类型列表
   */
  public getSupportedActionTypes(): game.C2SPacketType[] {
    return Array.from(ClientProtobufHandler.c2sMessageMap.keys());
  }

  /**
   * 获取支持的请求类型列表
   */
  // public getSupportedRequestTypes(): ClientRequestTypes[] {
  //   return Array.from(this.clientRequestMap.keys());
  // }

  /**
   * 尝试检测数据是否为protobuf格式
   * @param data 接收到的数据
   * @returns 是否为protobuf格式
   */
  public isProtobufData(data: any): boolean {
    // 如果数据是Buffer或Uint8Array，可能是protobuf
    if (Buffer.isBuffer(data) || data instanceof Uint8Array) {
      return true;
    }

    // 如果数据是对象且包含特定的protobuf标识，也可能是protobuf
    if (typeof data === 'object' && data !== null) {
      // 检查是否有protobuf的特征字段
      return false; // 对于JSON对象，返回false
    }

    return false;
  }
}

export default ClientProtobufHandler;
