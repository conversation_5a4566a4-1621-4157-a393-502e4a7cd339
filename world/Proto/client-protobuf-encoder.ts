import {game} from './generated/game_messages';
// import {ClientRequestTypes} from 'common/types';
import ClientProtobufHandler from "./client-protobuf-handler";

/**
 * 客户端Protobuf编码工具
 * 用于测试和示例，帮助客户端编码protobuf数据
 */
export class ClientProtobufEncoder {
  private static instance: ClientProtobufEncoder;

  public static getInstance(): ClientProtobufEncoder {
    if (!ClientProtobufEncoder.instance) {
      ClientProtobufEncoder.instance = new ClientProtobufEncoder();
    }
    return ClientProtobufEncoder.instance;
  }

  /**
   * 编码玩家动作消息
   * @param pid 动作类型
   * @param data 动作数据
   * @returns protobuf二进制数据
   */
  public encodePlayerAction(pid: game.C2SPacketType, data: any): Buffer | undefined {
    try {

      const MessageClass = ClientProtobufHandler.c2sMessageMap.get(pid);
      if (!MessageClass) {
        console.error(`Unsupported packet type: ${pid}`);
        return
      }
      const enterData = MessageClass.create(data);
      const actionData = MessageClass.encode(enterData).finish();
      // 创建动作消息包装器
      const actionMessage = game.PlayerActionMessage.create({
        pid: pid,
        actionData: actionData,
        timestamp: Date.now()
      });

      return Buffer.from(game.PlayerActionMessage.encode(actionMessage).finish());
    } catch (error) {
      console.error('Error encoding player action:', error);
      throw error;
    }
  }
  //
  // /**
  //  * 编码玩家请求消息
  //  * @param pid 请求类型
  //  * @param data 请求数据
  //  * @returns protobuf二进制数据
  //  */
  // public encodePlayerRequest(pid: ClientRequestTypes, data: any): Buffer {
  //   try {
  //     // 对于请求数据，通常使用JSON字符串编码
  //     const jsonString = JSON.stringify(data);
  //     const requestData = Buffer.from(jsonString, 'utf8');
  //
  //     // 创建请求消息包装器
  //     const requestMessage = game.PlayerRequestMessage.create({
  //       pid: pid,
  //       requestData: requestData,
  //       timestamp: Date.now()
  //     });
  //
  //     return Buffer.from(game.PlayerRequestMessage.encode(requestMessage).finish());
  //   } catch (error) {
  //     console.error('Error encoding player request:', error);
  //     throw error;
  //   }
  // }

  /**
   * 编码玩家聊天消息
   * @param chatId 聊天房间ID
   * @param pid 聊天消息类型
   * @param data 聊天数据
   * @returns protobuf二进制数据
   */
  public encodePlayerChat(chatId: number, pid: game.C2SPacketType, data: any): Buffer {
    try {
      let chatData: Uint8Array = new Uint8Array(0);

      // 如果是聊天消息，编码聊天内容
      if (pid === game.C2SPacketType.C2S_CHAT_MESSAGE && data) {
        const chatMessage = game.ChatMessage.create(data);
        chatData = game.ChatMessage.encode(chatMessage).finish();
      }

      // 创建聊天消息包装器
      const chatMessage = game.PlayerChatMessage.create({
        chatId: chatId,
        pid: pid,
        chatData: chatData,
        timestamp: Date.now()
      });

      return Buffer.from(game.PlayerChatMessage.encode(chatMessage).finish());
    } catch (error) {
      console.error('Error encoding player chat:', error);
      throw error;
    }
  }
}

export default ClientProtobufEncoder;
