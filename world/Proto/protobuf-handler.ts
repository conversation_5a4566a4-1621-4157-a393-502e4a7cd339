import {game} from './generated/game_messages';

/**
 * Protobuf消息处理器
 * 负责在JSON和Protobuf之间进行转换
 */
export class ProtobufHandler {
  private static instance: ProtobufHandler;

  // 消息类型映射：从protobuf枚举到protobuf消息类型
  static readonly s2cMessageMap = new Map<game.S2CPacketType, any>([
    [game.S2CPacketType.S2C_PLAYER_ENTER, game.PlayerEnter],
    [game.S2CPacketType.S2C_PLAYER_LEAVE, game.PlayerLeave],
    [game.S2CPacketType.S2C_PLAYER_POSITION, game.PlayerPosition],
    [game.S2CPacketType.S2C_PLAYER_ANIMATION, game.PlayerAnimation],
    [game.S2CPacketType.S2C_PLAYER_UPDATE, game.PlayerUpdate],
    [game.S2CPacketType.S2C_PET_POSITION, game.PetPosition],
    [game.S2CPacketType.S2C_PET_ANIMATION, game.PetAnimation],
    [game.S2CPacketType.S2C_CHAT_ENTER, game.ChatEnter],
    [game.S2CPacketType.S2C_CHAT_LEAVE, game.ChatLeave],
    [game.S2CPacketType.S2C_CHAT_MESSAGE, game.ChatMessage],
    [game.S2CPacketType.S2C_RED_PACKET_UPDATE, game.RedPacketUpdate],
  ]);

  public static getInstance(): ProtobufHandler {
    if (!ProtobufHandler.instance) {
      ProtobufHandler.instance = new ProtobufHandler();
    }
    return ProtobufHandler.instance;
  }

  /**
   * 测试protobuf解码功能
   */
  public testDecoding(): void {
    try {
      // 创建一个简单的测试消息
      const testMessage = game.GameMessage.create({
        pid: game.S2CPacketType.S2C_PLAYER_ENTER,
        data: new Uint8Array([1, 2, 3, 4]), // 简单的测试数据
        timestamp: Date.now()
      });

      const encoded = game.GameMessage.encode(testMessage).finish();
      console.log('Test encoding successful:', {
        originalMessage: testMessage,
        encodedLength: encoded.length,
        encodedBytes: Array.from(encoded.slice(0, 10))
      });

      // 尝试解码
      const decoded = game.GameMessage.decode(encoded);
      console.log('Test decoding successful:', decoded);

    } catch (error) {
      console.error('Protobuf test failed:', error);
    }
  }

  /**
   * 将protobuf二进制数据解码为JSON
   * @param buffer protobuf二进制数据
   * @returns 解码后的JSON数据
   */
  public decodeMessage(buffer: Buffer): { pid: game.S2CPacketType; data: any } {
    try {
      console.log('ProtobufHandler.decodeMessage input:', {
        bufferType: typeof buffer,
        isBuffer: Buffer.isBuffer(buffer),
        isUint8Array: buffer instanceof Uint8Array,
        length: buffer?.length,
        firstBytes: buffer?.length > 0 ? Array.from(buffer.slice(0, 10)) : 'empty'
      });

      // 验证输入数据
      if (!buffer || buffer.length === 0) {
        throw new Error('Empty or null buffer provided');
      }

      // 确保buffer是正确的格式
      let processedBuffer: Uint8Array;
      if (Buffer.isBuffer(buffer)) {
        processedBuffer = new Uint8Array(buffer);
      } else if (buffer instanceof Uint8Array) {
        processedBuffer = buffer;
      } else {
        throw new Error(`Invalid buffer type: ${typeof buffer}`);
      }

      console.log('Decoding GameMessage with processed buffer:', {
        length: processedBuffer.length,
        firstBytes: Array.from(processedBuffer.slice(0, 10))
      });

      // 解码包装消息
      const gameMessage = game.GameMessage.decode(processedBuffer);

      // 获取对应的消息类型
      const pid = gameMessage.pid;
      console.log('Decoded GameMessage:', {
        pid,
        dataLength: gameMessage.data?.length,
        timestamp: gameMessage.timestamp
      });

      if (!pid) {
        throw new Error(`Unknown packet type: ${gameMessage.pid}`);
      }

      const MessageClass = ProtobufHandler.s2cMessageMap.get(pid);
      if (!MessageClass) {
        throw new Error(`Unsupported packet type: ${pid}`);
      }

      // 验证内部数据
      if (!gameMessage.data || gameMessage.data.length === 0) {
        console.warn('Empty data in GameMessage, returning empty object');
        return {
          pid,
          data: {}
        };
      }

      console.log('Decoding inner message with data:', {
        messageClass: MessageClass.name,
        dataLength: gameMessage.data.length,
        firstBytes: Array.from(gameMessage.data.slice(0, 10))
      });

      // 解码内部消息
      const message = MessageClass.decode(gameMessage.data);

      // 转换为JSON格式
      const jsonData = message.toJSON()

      console.log('Successfully decoded message:', {
        pid,
        dataKeys: Object.keys(jsonData)
      });

      return {
        pid,
        data: jsonData
      };
    } catch (error) {
      console.error('Error decoding protobuf message:', {
        error: error.message,
        stack: error.stack,
        bufferInfo: {
          type: typeof buffer,
          length: buffer?.length,
          isBuffer: Buffer.isBuffer(buffer),
          firstBytes: buffer?.length > 0 ? Array.from(buffer.slice(0, 10)) : 'empty'
        }
      });
      throw error;
    }
  }

}

export default ProtobufHandler;
