import * as protobuf from 'protobufjs';
import { game } from './generated/game_messages';

/**
 * Protobuf消息处理器
 * 负责在JSON和Protobuf之间进行转换
 */
export class ProtobufHandler {
  private static instance: ProtobufHandler;

  // 消息类型映射：从protobuf枚举到protobuf消息类型
  static readonly s2cMessageMap = new Map<game.S2CPacketType, any>([
    [game.S2CPacketType.S2C_PLAYER_ENTER, game.PlayerEnter],
    [game.S2CPacketType.S2C_PLAYER_LEAVE, game.PlayerLeave],
    [game.S2CPacketType.S2C_PLAYER_POSITION, game.PlayerPosition],
    [game.S2CPacketType.S2C_PLAYER_ANIMATION, game.PlayerAnimation],
    [game.S2CPacketType.S2C_PLAYER_UPDATE, game.PlayerUpdate],
    [game.S2CPacketType.S2C_PET_POSITION, game.PetPosition],
    [game.S2CPacketType.S2C_PET_ANIMATION, game.PetAnimation],
    [game.S2CPacketType.S2C_CHAT_ENTER, game.ChatEnter],
    [game.S2CPacketType.S2C_CHAT_LEAVE, game.ChatLeave],
    [game.S2CPacketType.S2C_CHAT_MESSAGE, game.ChatMessage],
    [game.S2CPacketType.S2C_RED_PACKET_UPDATE, game.RedPacketUpdate],
  ]);

  public static getInstance(): ProtobufHandler {
    if (!ProtobufHandler.instance) {
      ProtobufHandler.instance = new ProtobufHandler();
    }
    return ProtobufHandler.instance;
  }

  /**
   * 将protobuf二进制数据解码为JSON
   * @param buffer protobuf二进制数据
   * @returns 解码后的JSON数据
   */
  public decodeMessage(buffer: Buffer): { pid: game.S2CPacketType; data: any } {
    try {
      // 解码包装消息
      const gameMessage = game.GameMessage.decode(buffer);

      // 获取对应的消息类型
      const pid = gameMessage.pid;
      if (!pid) {
        throw new Error(`Unknown packet type: ${gameMessage.pid}`);
      }

      const MessageClass = ProtobufHandler.s2cMessageMap.get(pid);
      if (!MessageClass) {
        throw new Error(`Unsupported packet type: ${pid}`);
      }

      // 解码内部消息
      const message = MessageClass.decode(gameMessage.data);

      // 转换为JSON格式
      const jsonData = message.toJSON()

      return {
        pid,
        data: jsonData
      };
    } catch (error) {
      console.error('Error decoding protobuf message:', error);
      throw error;
    }
  }

}

export default ProtobufHandler;
