#!/bin/bash

# 检查参数数量
if [ "$#" -ne 15 ]; then
  echo "Usage: $0 The number of parameters does not match."
  exit 1
fi

# 参数赋值
ORD_SERVER=$1
USE_CONFIG=$2
AVATAR_VERSION=$3
APP_NETWORK=$4
REQUEST_URL=$5
ENVIRONMENT=$6
ORD_NFT_IMG_SERVER=$7
# shellcheck disable=SC2034
BUILD_TAG=$8
DOMAIN=$9
SHARE_DOMAIN=${10}
CDN_SERVER=${11}
CDN_VERSION=${12}
AI_REQUEST_URL=${13}
GAME_REQUEST_URL=${14}
GAME_SOCKET_URL=${15}

# 获取当前工作目录
ROOT_PATH=$(pwd)

# 输出当前工作目录
echo "当前工作目录是: $ROOT_PATH"

# 创建 .env 文件
cat <<EOF > .env
ORD_SERVER=$ORD_SERVER
ORD_NFT_IMG_SERVER=$ORD_NFT_IMG_SERVER
USE_CONFIG=$USE_CONFIG
AVATAR_VERSION=$AVATAR_VERSION
APP_NETWORK=$APP_NETWORK
REQUEST_URL=$REQUEST_URL
DOMAIN=$DOMAIN
SHARE_DOMAIN=$SHARE_DOMAIN
CDN_SERVER=$CDN_SERVER
CDN_VERSION=$CDN_VERSION
AI_REQUEST_URL=$AI_REQUEST_URL
ENVIRONMENT=$ENVIRONMENT
GAME_REQUEST_URL=$GAME_REQUEST_URL
GAME_SOCKET_URL=$GAME_SOCKET_URL
EOF

# 设置 NPM 镜像源
echo "设置镜像 http://registry.npm.taobao.org"
npm config set registry http://registry.npm.taobao.org

git log -10 --pretty=format:"%h - %an, %ad : %s" --date=iso

cd "$ROOT_PATH/AvatarOrdinalsBrowser" || exit
# 更新AvatarOrdinalsBrowser子模块并安装依赖
git tag -d "$BUILD_TAG" 2>/dev/null|| echo "子模块标签 $BUILD_TAG 不存在，继续执行"
git reset --hard HEAD
git pull
git checkout "$BUILD_TAG"
git pull
git log -10 --pretty=format:"%h - %an, %ad : %s" --date=iso

cd "$ROOT_PATH" || exit
# 安装依赖并构建项目
rm -rf node_modules
npm install --force
npm run build

# 定义项目发布路径
DEST_PATH="/var/www/html"
OUT_DIR="out"

# 执行环境相关操作
case $ENVIRONMENT in
  beta)
    TARGET="beta"
    ;;
  test)
    TARGET="test"
    ;;
  dev)
    TARGET="dev"
    ;;
  release)
    TARGET="out2"
    ;;
  online)
    TARGET="out"
    ;;
  *)
    echo "Invalid environment: $ENVIRONMENT"
    exit 1
    ;;
esac

# 删除旧版本，移动新版本并重载 Nginx
sudo rm -rf "$DEST_PATH/$TARGET"
if [ "$TARGET" != "out" ]; then
  sudo mv "$OUT_DIR" "$TARGET"
fi
sudo mv "$TARGET" "$DEST_PATH/"
nginx -s reload

# 可选的传输与解压操作
# echo "scp $ENGINE_VERSION.tar.gz  app@192.168.40.157:/home/<USER>/bmx/room/gameserver/"
# scp "$ENGINE_VERSION.tar.gz" app@192.168.40.157:/home/<USER>/bmx/room/gameserver/
# echo "ssh app@192.168.40.157 -t -t \"tar -xzvf /home/<USER>/bmx/room/gameserver/$ENGINE_VERSION.tar.gz -C /home/<USER>/bmx/room/gameserver/\""
# ssh app@192.168.40.157 -t -t "tar -xzvf /home/<USER>/bmx/room/gameserver/$ENGINE_VERSION.tar.gz -C /home/<USER>/bmx/room/gameserver/"

exit 0