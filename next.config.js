/** @type {import('next').NextConfig} */
const path = require('path');

const nextConfig = {
  output: 'export',
  reactStrictMode: false,
  pageExtensions: ['mdx', 'md', 'jsx', 'js', 'tsx', 'ts'],
  productionBrowserSourceMaps: false,
  typescript: {
    ignoreBuildErrors: true,
  },
  exportPathMap: async function (defaultPathMap) {
    return {
      ...defaultPathMap,
      '/': { page: '/' },
    };
  },
  images: {
    unoptimized: true,
  },
  assetPrefix: './',
  basePath: '',
  env:{
    ORD_SERVER: process.env.ORD_SERVER,
    USE_CONFIG: process.env.USE_CONFIG,
    ORD_NFT_IMG_SERVER: process.env.ORD_NFT_IMG_SERVER,
    AVATAR_VERSION: process.env.AVATAR_VERSION,
    APP_NETWORK: process.env.APP_NETWORK,
    REQUEST_URL: process.env.REQUEST_URL,
    DOMAIN: process.env.DOMAIN,
    SHARE_DOMAIN: process.env.SHARE_DOMAIN,
    CDN_SERVER: process.env.CDN_SERVER,
    CDN_VERSION: process.env.CDN_VERSION,
    AI_REQUEST_URL: process.env.AI_REQUEST_URL,
    ENVIRONMENT: process.env.ENVIRONMENT,
    LOCAL_HOSTS_DOMAIN: process.env.LOCAL_HOSTS_DOMAIN,
    GAME_REQUEST_URL: process.env.GAME_REQUEST_URL,
    GAME_SOCKET_URL: process.env.GAME_SOCKET_URL,
  },
  webpack: function (config, options) {
    config.experiments = {
      asyncWebAssembly: true,
      topLevelAwait: true,
      layers: true,
    };

    // 添加路径别名配置
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(__dirname),
      '@root': path.resolve(__dirname),
      '@rootComponents': path.resolve(__dirname, 'components'),
      '@rootHooks': path.resolve(__dirname, 'hooks'),
      '@rootUtils': path.resolve(__dirname, 'utils'),
      '@rootStyles': path.resolve(__dirname, 'styles'),
    };

    config.optimization.splitChunks = {
      chunks: 'all',
      maxSize: 1024 * 1024, // 1MB
      minSize: 20000, // 20kb
      cacheGroups: {
        default: false,
        vendors: false,
        framework: {
          name: 'framework',
          test: /[\\/]node_modules[\\/](react|react-dom|scheduler|prop-types|use-subscription)[\\/]/,
          priority: 40,
          chunks: 'all',
        },
        lib: {
          test: /[\\/]node_modules[\\/]/,
          name(module, chunks, cacheGroupKey) {
            return `${cacheGroupKey}-${module.type}`;
          },
          priority: 30,
          chunks: 'all',
          minChunks: 2,
        },
        commons: {
          name: 'commons',
          minChunks: 2,
          priority: 20,
        },
        shared: {
          name: 'shared',
          priority: 10,
          minChunks: 2,
          reuseExistingChunk: true,
        },
      },
    };
/*    config.optimization.splitChunks = {
      cacheGroups: {
        default: false,
        vendors: false,
      },
    };*/

    config.optimization.runtimeChunk = false;
    config.module.rules.push({
      test: /\.(ttf|woff|woff2|eot|otf)$/,
      use: [
        options.defaultLoaders.babel,
        {
          loader: 'url-loader'
        },
      ],
    })
    config.module.rules.push({
      test: /\.(glb)$/,
      use: [
        options.defaultLoaders.babel,
        {
          loader: 'url-loader'
        },
      ],
    })
    return config;
  },
  compiler: {
    styledComponents: true,
  },
};
module.exports = nextConfig;
