import {useDispatch, useSelector} from "react-redux";
import {IAppState, NETWORK_ENUM, SUPPORT_WALLET_ENUM} from "../constant/type";
import toast from "react-hot-toast";
import {resetStates, setBtcAddress, setBtcWallet, setDefaultInscriptionId,} from "../store/app";
import {useEffect, useRef, useState} from "react";
import {basicLogin, basicQuickLogin, getLoginSignContent, getSystemTime, setRequestHeaders,} from "../server";
import {getLocalSession, getPublicKey, setLocalSession} from "../utils";
// import moment from "moment";
import dayjs from "dayjs";
import {APP_NETWORK} from "../constant";
import {useUserBasicInfo} from "./useUserBasicInfo";

const LOCAL_CONNECTED_WALLET = "CONNECTED_WALLET";
export default function useConnectWallet(init = false) {
  const { btcWallet, btcAddress } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const dispatch = useDispatch();
  const [isPending, setIsPending] = useState(false);
  const [isConnected, setIsConnected] = useState(!!btcAddress);
  const walletPendingRef: any = useRef(false);
  const reTryRef: any = useRef(null);
  const { refreshUserBasicInfo } = useUserBasicInfo({ autoFetch: false });

  // DOCS: https://docs.unisat.io/dev/unisat-developer-center/unisat-wallet
  const connectToUniSat = async (reTry?: number) => {
    setIsPending(true);
    const unisat = window.unisat;
    if (!unisat) {
      if (!reTry || reTry < 20) {
        clearTimeout(reTryRef.current);
        reTryRef.current = setTimeout(
          () => connectToUniSat(reTry ? reTry + 1 : 1),
          100
        );
        setIsPending(false);
        return;
      }
      toast.error("Please install unisat wallet", {
        id: "install-wallet",
        position: "top-right",
        duration: 6000,
      });
      setIsPending(false);
      return;
    }
    walletPendingRef.current = true;
    try {
      await unisat.requestAccounts();
      const { enum: networkEnum } = await unisat.getChain();
      if (networkEnum !== APP_NETWORK) {
        const { enum: nowNetworkEnum } = await unisat.switchChain(APP_NETWORK);
        if (nowNetworkEnum !== APP_NETWORK) {
          const toNetworkName =
            networkEnum === NETWORK_ENUM.FRACTAL_BITCOIN_TESTNET
              ? "Fractal Bitcoin Testnet"
              : "Fractal Bitcoin Mainnet";
          toast.error(`Please switch network to ${toNetworkName}`, {
            duration: 6000,
          });
          setIsPending(false);
          return;
        }
      }
      // 签名登录
      const [address] = await unisat.getAccounts();
      // if (address.indexOf("bc1p") !== 0) {
      //   toast.error("Currently supports Taproot address only.", {
      //     duration: 6000,
      //     id: "bc1p",
      //   });
      //   setIsPending(false);
      //   return;
      // }
      const systemTime = await getSystemTime().then(
        (res: any) => res.data.data
      );
      const { sessionId: localSessionId, expires: localExpires } =
        getLocalSession(address);
      if (localExpires < systemTime) {
        const signContent = await getLoginSignContent().then((res: any) => {
          return res.data.data
            .replace(`address: %s`, `address: ${address}`)
            .replace(
              `Operation time: %s`,
              `Operation time: ${dayjs(systemTime).format(
                "YYYY-MM-DD HH:mm:ss"
              )}`
            );
        });
        const signRes = await unisat.signMessage(signContent);
        const { expires, sessionId, inscriptionId } = await basicLogin({
          address: address,
          pubKey: await getPublicKey(btcWallet),
          signContent: signContent,
          signRes: signRes,
        }).then((res) => {
          return res.data.data;
        });
        setLocalSession(address, sessionId, expires);
        setRequestHeaders(sessionId, address);
        dispatch(setDefaultInscriptionId(inscriptionId || ""));
      } else {
        setRequestHeaders(localSessionId, address);
        // 统计日活
        basicQuickLogin().then((res: any) => {
          dispatch(
            setDefaultInscriptionId(res.data?.data?.inscriptionId || "")
          );
        });
      }
      /////
      dispatch(setBtcAddress(address));
      window.localStorage.setItem(
        LOCAL_CONNECTED_WALLET,
        SUPPORT_WALLET_ENUM.unisat
      );
      dispatch(setBtcWallet(SUPPORT_WALLET_ENUM.unisat));
      setIsConnected(true);
      await refreshUserBasicInfo();



      const accountChange = async (accounts: Array<string>) => {
        // TODO: 心跳监测，如果账号发生变化，比如钱包到期自动断开，那么暂时不需要重置
        // setRequestHeaders("", "");
        // dispatch(setBtcAddress(""));
        // window.localStorage.removeItem(LOCAL_CONNECTED_WALLET);
        if (
          accounts.length > 0 &&
          address !== accounts[0] &&
          !walletPendingRef.current
        ) {
          connectToUniSat();
        }
      };
      unisat.removeListener("accountsChanged", accountChange);
      unisat.on("accountsChanged", accountChange);
    } catch (e: any) {
      setRequestHeaders("", "");
      dispatch(setBtcAddress(""));
      window.localStorage.removeItem(LOCAL_CONNECTED_WALLET);
      setIsConnected(false);
    }
    walletPendingRef.current = false;
    setIsPending(false);
  };

  // DOCS: https://www.okx.com/zh-hans/web3/build/docs/sdks/chains/bitcoin/provider-fractal
  const connectToOkx = async () => {
    setIsPending(true);
    const okxwallet = window.okxwallet;
    if (!okxwallet) {
      toast.error("Please install okx wallet", {
        id: "install-wallet",
        position: "top-right",
        duration: 6000,
      });
      setIsPending(false);
      return;
    }
    try {
      walletPendingRef.current = true;
      const fractalBitcoin = okxwallet.fractalBitcoin;
      const result = await fractalBitcoin.connect();
      const address = result.address;
      // if (address.indexOf("bc1p") !== 0) {
      //   toast.error("Currently supports Taproot address only.", {
      //     duration: 6000,
      //     id: "bc1p",
      //   });
      //   setIsPending(false);
      //   return;
      // }
      const systemTime = await getSystemTime().then(
        (res: any) => res.data.data
      );
      const { sessionId: localSessionId, expires: localExpires } =
        getLocalSession(address);
      if (localExpires < systemTime) {
        const signContent = await getLoginSignContent().then((res: any) => {
          return res.data.data
            .replace(`address: %s`, `address: ${address}`)
            .replace(
              `Operation time: %s`,
              `Operation time: ${dayjs(systemTime).format(
                "YYYY-MM-DD HH:mm:ss"
              )}`
            );
        });
        const signRes = await fractalBitcoin.signMessage(signContent, "ecdsa");
        const { expires, sessionId, inscriptionId } = await basicLogin({
          address: address,
          pubKey: await getPublicKey(SUPPORT_WALLET_ENUM.okx),
          signContent: signContent,
          signRes: signRes,
        }).then((res) => {
          return res.data.data;
        });
        setLocalSession(address, sessionId, expires);
        setRequestHeaders(sessionId, address);
        dispatch(setDefaultInscriptionId(inscriptionId || ""));
      } else {
        setRequestHeaders(localSessionId, address);
        // 统计日活
        basicQuickLogin().then((res: any) => {
          dispatch(
            setDefaultInscriptionId(res.data?.data?.inscriptionId || "")
          );
        });
      }
      dispatch(setBtcAddress(address));
      window.localStorage.setItem(
        LOCAL_CONNECTED_WALLET,
        SUPPORT_WALLET_ENUM.okx
      );
      dispatch(setBtcWallet(SUPPORT_WALLET_ENUM.okx));
      setIsConnected(true);

      await refreshUserBasicInfo();

      window.okxwallet.bitcoin.on("accountsChanged", (accounts: any) => {
        if (
          accounts.length > 0 &&
          address !== accounts[0] &&
          !walletPendingRef.current
        ) {
          connectToOkx();
        } else {
          setRequestHeaders("", "");
          dispatch(setBtcAddress(""));
          window.localStorage.removeItem(LOCAL_CONNECTED_WALLET);
        }
      });
    } catch (e: any) {
      setRequestHeaders("", "");
      dispatch(setBtcAddress(""));
      window.localStorage.removeItem(LOCAL_CONNECTED_WALLET);
      setIsConnected(false);
    }
    setIsPending(false);
  };
  const connectWallet = (wallet: SUPPORT_WALLET_ENUM) => {
    walletPendingRef.current = false;
    if (wallet === SUPPORT_WALLET_ENUM.unisat) {
      connectToUniSat();
    } else if (wallet === SUPPORT_WALLET_ENUM.okx) {
      connectToOkx();
    } else {
      toast.error("Wallet not supported", { duration: 6000 });
    }
  };
  const disconnectWallet = () => {
    localStorage.removeItem(LOCAL_CONNECTED_WALLET);
    dispatch(setBtcAddress(""));
    setIsConnected(false);
    // dispatch(resetStates());
  };
  useEffect(() => {
    // 获取浏览器参数带有 connect
    const urlParams = new URLSearchParams(window.location.search);
    const isNeedConnect = urlParams.get("connect");
    if (init) {
      const walletId = localStorage.getItem(LOCAL_CONNECTED_WALLET);
      if (
        walletId === SUPPORT_WALLET_ENUM.unisat ||
        walletId === SUPPORT_WALLET_ENUM.okx
      ) {
        connectWallet(walletId as SUPPORT_WALLET_ENUM);
      }
    } else if (
      isNeedConnect === SUPPORT_WALLET_ENUM.unisat ||
      isNeedConnect === SUPPORT_WALLET_ENUM.okx
    ) {
      connectWallet(isNeedConnect as SUPPORT_WALLET_ENUM);
    }
  }, []);
  useEffect(() => {
    setIsConnected(!!btcAddress);
  }, [btcAddress]);

  useEffect(() => {
    if (!btcAddress || !isConnected) {
      dispatch(resetStates());
    }
  }, [btcAddress, isConnected]);

  return {
    btcWallet,
    btcAddress,
    connectWallet,
    disconnectWallet,
    isPending,
    isConnected,
  };
}
