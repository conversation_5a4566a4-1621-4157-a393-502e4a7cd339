interface TheLonelyBitIconProps {
  style?: React.CSSProperties;
}

const TheLonelyBitIcon: React.FC<TheLonelyBitIconProps> = ({ style }) => {
  return (
    <svg
      style={{
        ...style,
        position: "relative",
        left: "-5px"
      }}
      width="280"
      height="44"
      viewBox="0 0 318 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_d_4150_18328)">
        <mask
          id="mask0_4150_18328"
          style={{ maskType: "alpha" }}
          maskUnits="userSpaceOnUse"
          x="4"
          y="2"
          width="310"
          height="40"
        >
          <path
            d="M9.37109 34V27.2617H15.0352V9.25391H11.5391L10.7578 15.8359H4.3125V2.90625H35.9141V15.8359H29.4688L28.6875 9.25391H25.1914V27.2617H30.8555V34H9.37109ZM37.6328 34V28.1406H40.4648V10.0156H37.6328V4.68359L49.8398 3.53125V15.25C51.5456 13.5573 53.7917 12.7109 56.5781 12.7109C58.9349 12.7109 60.6602 13.3815 61.7539 14.7227C62.8607 16.0508 63.4141 18.056 63.4141 20.7383V28.1406H66.1484V34H54.0391V20.7383C54.0391 19.9831 53.8763 19.4362 53.5508 19.0977C53.2253 18.7461 52.7956 18.5703 52.2617 18.5703C51.819 18.5703 51.3763 18.7005 50.9336 18.9609C50.5039 19.2214 50.1393 19.5794 49.8398 20.0352V28.1406H51.5977V34H37.6328ZM80.5039 34.3516C76.6367 34.3516 73.6029 33.3815 71.4023 31.4414C69.2148 29.4883 68.1211 26.7865 68.1211 23.3359C68.1211 21.1615 68.5964 19.2799 69.5469 17.6914C70.5104 16.1029 71.8516 14.8789 73.5703 14.0195C75.2891 13.1471 77.2878 12.7109 79.5664 12.7109C81.793 12.7109 83.7005 13.1211 85.2891 13.9414C86.8776 14.7487 88.0885 15.8815 88.9219 17.3398C89.7682 18.7852 90.1914 20.4583 90.1914 22.3594C90.1914 22.8021 90.1719 23.2318 90.1328 23.6484C90.0938 24.0521 90.0482 24.4232 89.9961 24.7617H77.4375C77.6589 26.0898 78.2578 27.0469 79.2344 27.6328C80.224 28.2057 81.4284 28.4922 82.8477 28.4922C84.0456 28.4922 85.1849 28.2839 86.2656 27.8672C87.3594 27.4375 88.2839 26.9036 89.0391 26.2656L89.5664 26.5V32.1055C88.9414 32.5352 88.1341 32.9193 87.1445 33.2578C86.1549 33.5964 85.0807 33.8633 83.9219 34.0586C82.776 34.2539 81.6367 34.3516 80.5039 34.3516ZM80.0742 17.5156C79.2018 17.5156 78.5443 17.8542 78.1016 18.5312C77.6589 19.2083 77.4115 20.1523 77.3594 21.3633H82.7891C82.737 20.1523 82.4896 19.2083 82.0469 18.5312C81.6172 17.8542 80.9596 17.5156 80.0742 17.5156ZM92.4766 34V27.2617H95.6016V9.64453H92.4766V2.90625H110.055V9.64453H105.758V27.3594H112.125L112.906 20.0742H119.352V34H92.4766ZM133.551 34.3516C131.103 34.3516 128.987 33.9154 127.203 33.043C125.432 32.1706 124.065 30.9271 123.102 29.3125C122.151 27.6979 121.676 25.7773 121.676 23.5508C121.676 21.3112 122.151 19.3841 123.102 17.7695C124.065 16.1549 125.432 14.9115 127.203 14.0391C128.987 13.1536 131.103 12.7109 133.551 12.7109C135.999 12.7109 138.108 13.1536 139.879 14.0391C141.663 14.9115 143.03 16.1549 143.98 17.7695C144.944 19.3841 145.426 21.3112 145.426 23.5508C145.426 25.7773 144.944 27.6979 143.98 29.3125C143.03 30.9271 141.663 32.1706 139.879 33.043C138.108 33.9154 135.999 34.3516 133.551 34.3516ZM133.551 28.668C135.309 28.668 136.188 26.9557 136.188 23.5312C136.188 20.1068 135.309 18.3945 133.551 18.3945C131.793 18.3945 130.914 20.1068 130.914 23.5312C130.914 26.9557 131.793 28.668 133.551 28.668ZM147.926 34V28.1406H150.758V19.1758H147.926V13.8438L159.938 12.6914V15.3477C160.745 14.5013 161.754 13.8503 162.965 13.3945C164.176 12.9258 165.478 12.6914 166.871 12.6914C169.228 12.6914 170.953 13.362 172.047 14.7031C173.154 16.0312 173.707 18.0365 173.707 20.7188V28.1406H176.441V34H164.332V20.7188C164.332 19.9635 164.169 19.4167 163.844 19.0781C163.518 18.7266 163.089 18.5508 162.555 18.5508C162.112 18.5508 161.669 18.681 161.227 18.9414C160.797 19.2018 160.432 19.5599 160.133 20.0156V28.1406H161.891V34H147.926ZM190.777 34.3516C186.91 34.3516 183.876 33.3815 181.676 31.4414C179.488 29.4883 178.395 26.7865 178.395 23.3359C178.395 21.1615 178.87 19.2799 179.82 17.6914C180.784 16.1029 182.125 14.8789 183.844 14.0195C185.562 13.1471 187.561 12.7109 189.84 12.7109C192.066 12.7109 193.974 13.1211 195.562 13.9414C197.151 14.7487 198.362 15.8815 199.195 17.3398C200.042 18.7852 200.465 20.4583 200.465 22.3594C200.465 22.8021 200.445 23.2318 200.406 23.6484C200.367 24.0521 200.322 24.4232 200.27 24.7617H187.711C187.932 26.0898 188.531 27.0469 189.508 27.6328C190.497 28.2057 191.702 28.4922 193.121 28.4922C194.319 28.4922 195.458 28.2839 196.539 27.8672C197.633 27.4375 198.557 26.9036 199.312 26.2656L199.84 26.5V32.1055C199.215 32.5352 198.408 32.9193 197.418 33.2578C196.428 33.5964 195.354 33.8633 194.195 34.0586C193.049 34.2539 191.91 34.3516 190.777 34.3516ZM190.348 17.5156C189.475 17.5156 188.818 17.8542 188.375 18.5312C187.932 19.2083 187.685 20.1523 187.633 21.3633H193.062C193.01 20.1523 192.763 19.2083 192.32 18.5312C191.891 17.8542 191.233 17.5156 190.348 17.5156ZM202.125 34V28.1406H205.152V10.1328H202.125V4.80078L214.527 3.53125V28.1406H217.555V34H202.125ZM223.668 41.7734V35.9141H228.492L229.195 34.5273L219.898 18.9219H218.043V13.0625H232.164V18.9219H229.82L233.57 25.7188L236.949 18.9219H234.605V13.0625H244.508V18.9219H242.652L234.195 35.9141H237.027V41.7734H223.668ZM246.734 34V27.2617H249.859V9.64453H246.734V2.90625H263.59C267.757 2.90625 270.901 3.6224 273.023 5.05469C275.159 6.48698 276.227 8.6224 276.227 11.4609C276.227 12.9583 275.855 14.2865 275.113 15.4453C274.384 16.6042 273.323 17.4896 271.93 18.1016C273.427 18.7005 274.605 19.6055 275.465 20.8164C276.324 22.0143 276.754 23.4531 276.754 25.1328C276.754 27.9844 275.712 30.1784 273.629 31.7148C271.546 33.2383 268.564 34 264.684 34H246.734ZM262.398 9.44922H259.781V15.4258H262.398C264.82 15.4258 266.031 14.4297 266.031 12.4375C266.031 10.4453 264.82 9.44922 262.398 9.44922ZM259.781 27.457H262.809C265.309 27.457 266.559 26.3568 266.559 24.1562C266.559 21.9557 265.309 20.8555 262.809 20.8555H259.781V27.457ZM279.41 34V28.1406H282.438V19.1758H279.41V13.8438L291.812 12.6914V28.1406H294.84V34H279.41ZM287.125 11.3828C285.419 11.3828 284.052 10.9727 283.023 10.1523C282.008 9.33203 281.5 8.24479 281.5 6.89062C281.5 5.53646 282.008 4.46875 283.023 3.6875C284.039 2.89323 285.413 2.49609 287.145 2.49609C288.915 2.49609 290.289 2.88021 291.266 3.64844C292.255 4.41667 292.75 5.4974 292.75 6.89062C292.75 8.27083 292.242 9.36458 291.227 10.1719C290.224 10.9792 288.857 11.3828 287.125 11.3828ZM308.609 34.3516C305.523 34.3516 303.141 33.6484 301.461 32.2422C299.781 30.8229 298.941 28.7721 298.941 26.0898V18.9219H295.719V13.5898L298.941 13.2773V8.58984L308.316 6.53906V13.0625H313.043V18.9219H308.316V25.8945C308.316 26.6758 308.505 27.2878 308.883 27.7305C309.273 28.1602 309.859 28.375 310.641 28.375C311.096 28.375 311.48 28.3359 311.793 28.2578C312.118 28.1667 312.424 28.0755 312.711 27.9844L313.238 28.2188V33.8047C312.496 33.974 311.747 34.1042 310.992 34.1953C310.25 34.2995 309.456 34.3516 308.609 34.3516Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask0_4150_18328)">
          <rect x="-22" y="-19" width="342" height="70" fill="#FFE683" />
          <rect x="-22" y="-19" width="342" height="70" fill="white" />
          <rect
            x="154.274"
            y="-87"
            width="89.9184"
            height="149"
            transform="rotate(25.5545 154.274 -87)"
            fill="white"
          />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_d_4150_18328"
          x="0.3125"
          y="2.5"
          width="316.926"
          height="47.2734"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_4150_18328"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_4150_18328"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};

export default TheLonelyBitIcon;
