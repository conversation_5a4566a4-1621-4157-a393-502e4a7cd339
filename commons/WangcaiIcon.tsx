interface WangcaiIconProps {
  style?: React.CSSProperties;
}

const WangcaiIcon: React.FC<WangcaiIconProps> = ({ style }) => {
  return (
    <svg
      style={{
        ...style,
        position: "relative",
        left: "-5px",
      }}
      width="200"
      height="45"
      viewBox="0 0 291 65"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_d_4150_18268)">
        <path
          d="M19.0312 45L8.69531 10.9023H4.73047V1.46875H29.1758V10.9023H24.6641L30.1875 31.5742L38.8828 1.46875H49.6289L57.9961 31.875L63.7383 10.9023H59.1992V1.46875H77.3008V10.9023H73.3359L63 45H47.3047L41.207 24.5195L35 45H19.0312ZM85.5039 45.4922C83.6263 45.4922 81.9492 45.1185 80.4727 44.3711C79.0143 43.6055 77.8659 42.5573 77.0273 41.2266C76.207 39.8958 75.7969 38.3646 75.7969 36.6328C75.7969 33.9349 76.8359 31.7839 78.9141 30.1797C80.9922 28.5755 83.763 27.7734 87.2266 27.7734C88.4297 27.7734 89.5781 27.8464 90.6719 27.9922C91.7656 28.138 92.8776 28.3841 94.0078 28.7305V28.1289C94.0078 26.4701 93.4974 25.2669 92.4766 24.5195C91.474 23.7721 89.8607 23.3984 87.6367 23.3984C86.0326 23.3984 84.4102 23.6081 82.7695 24.0273C81.1289 24.4466 79.6341 25.0299 78.2852 25.7773L77.5469 25.4492V17.4648C79.4609 16.7174 81.6302 16.1523 84.0547 15.7695C86.4974 15.3867 88.9492 15.1953 91.4102 15.1953C96.8607 15.1953 100.844 16.1068 103.359 17.9297C105.875 19.7526 107.133 22.6784 107.133 26.707V36.7969H110.688V45H95.7578L94.418 40.707C93.5612 42.2018 92.3398 43.3776 90.7539 44.2344C89.1862 45.0729 87.4362 45.4922 85.5039 45.4922ZM91.2188 38.4922C91.7109 38.4922 92.2214 38.4102 92.75 38.2461C93.2969 38.0638 93.7161 37.8268 94.0078 37.5352V32.9688C93.5521 32.7682 93.0872 32.6315 92.6133 32.5586C92.1576 32.4857 91.7292 32.4492 91.3281 32.4492C90.3984 32.4492 89.6146 32.7227 88.9766 33.2695C88.3568 33.8164 88.0469 34.5456 88.0469 35.457C88.0469 36.3867 88.3385 37.125 88.9219 37.6719C89.5052 38.2188 90.2708 38.4922 91.2188 38.4922ZM114.324 45V36.7969H118.289V24.2461H114.324V16.7812L131.141 15.168V18.8867C132.271 17.7018 133.684 16.7904 135.379 16.1523C137.074 15.4961 138.897 15.168 140.848 15.168C144.147 15.168 146.562 16.1068 148.094 17.9844C149.643 19.8438 150.418 22.651 150.418 26.4062V36.7969H154.246V45H137.293V26.4062C137.293 25.349 137.065 24.5833 136.609 24.1094C136.154 23.6172 135.552 23.3711 134.805 23.3711C134.185 23.3711 133.565 23.5534 132.945 23.918C132.344 24.2826 131.833 24.7839 131.414 25.4219V36.7969H133.875V45H114.324ZM180.633 37.4805C184.188 37.4805 186.867 38.2005 188.672 39.6406C190.495 41.0807 191.406 43.1315 191.406 45.793C191.406 48.1081 190.613 50.0586 189.027 51.6445C187.46 53.2305 185.263 54.4245 182.438 55.2266C179.612 56.0286 176.312 56.4297 172.539 56.4297C168.802 56.4297 165.758 56.0924 163.406 55.418C161.073 54.7617 159.359 53.8776 158.266 52.7656C157.19 51.6719 156.652 50.4779 156.652 49.1836C156.652 48.1992 157.017 47.306 157.746 46.5039C158.493 45.7018 159.441 45.1185 160.59 44.7539C159.277 43.6966 158.621 42.3112 158.621 40.5977C158.621 39.0846 159.159 37.8177 160.234 36.7969C161.328 35.7578 162.878 34.9922 164.883 34.5C162.44 33.7891 160.408 32.6771 158.785 31.1641C157.181 29.651 156.379 27.7279 156.379 25.3945C156.379 22.332 157.792 19.8711 160.617 18.0117C163.443 16.1341 167.572 15.1953 173.004 15.1953C175.866 15.1953 178.427 15.5599 180.688 16.2891L192.062 15.168V23.6445H188.617C188.745 24.2096 188.809 24.793 188.809 25.3945C188.809 28.457 187.35 30.918 184.434 32.7773C181.517 34.6185 177.361 35.5391 171.965 35.5391C170.652 35.5391 169.996 35.8581 169.996 36.4961C169.996 37.1523 170.652 37.4805 171.965 37.4805H180.633ZM173.004 20.9922C171.892 20.974 170.953 21.3568 170.188 22.1406C169.422 22.9062 169.039 23.9727 169.039 25.3398C169.039 26.7253 169.413 27.8099 170.16 28.5938C170.926 29.3776 171.874 29.7604 173.004 29.7422C174.134 29.724 175.073 29.332 175.82 28.5664C176.586 27.7826 176.969 26.707 176.969 25.3398C176.969 23.9909 176.586 22.9336 175.82 22.168C175.073 21.4023 174.134 21.0104 173.004 20.9922ZM164.965 47.6523C164.965 48.7461 165.53 49.6029 166.66 50.2227C167.79 50.8607 169.477 51.1797 171.719 51.1797C174.161 51.1797 175.921 50.8971 176.996 50.332C178.072 49.7669 178.609 49.0469 178.609 48.1719C178.609 47.2604 177.953 46.8047 176.641 46.8047H168.246C167.134 46.8047 166.104 46.7318 165.156 46.5859C165.029 46.9323 164.965 47.2878 164.965 47.6523ZM209.836 45.4922C204.659 45.4922 200.576 44.1341 197.586 41.418C194.615 38.6836 193.129 34.901 193.129 30.0703C193.129 27.0443 193.758 24.4193 195.016 22.1953C196.292 19.9714 197.987 18.2487 200.102 17.0273C202.234 15.806 204.577 15.1953 207.129 15.1953C208.988 15.1953 210.62 15.5234 212.023 16.1797C213.427 16.8177 214.621 17.7018 215.605 18.832L216.699 15.6875H222.742V27.8281H214.867C214.721 26.2604 214.293 25.0938 213.582 24.3281C212.871 23.5443 211.978 23.1523 210.902 23.1523C209.681 23.1523 208.587 23.681 207.621 24.7383C206.673 25.7773 206.199 27.5182 206.199 29.9609C206.199 32.4948 206.865 34.3086 208.195 35.4023C209.526 36.4779 211.212 37.0156 213.254 37.0156C214.785 37.0156 216.262 36.7148 217.684 36.1133C219.124 35.5117 220.363 34.7734 221.402 33.8984L222.141 34.2266V42.3477C221.284 42.9492 220.181 43.487 218.832 43.9609C217.483 44.4349 216.025 44.8086 214.457 45.082C212.889 45.3555 211.349 45.4922 209.836 45.4922ZM235.949 45.4922C234.072 45.4922 232.395 45.1185 230.918 44.3711C229.46 43.6055 228.311 42.5573 227.473 41.2266C226.652 39.8958 226.242 38.3646 226.242 36.6328C226.242 33.9349 227.281 31.7839 229.359 30.1797C231.438 28.5755 234.208 27.7734 237.672 27.7734C238.875 27.7734 240.023 27.8464 241.117 27.9922C242.211 28.138 243.323 28.3841 244.453 28.7305V28.1289C244.453 26.4701 243.943 25.2669 242.922 24.5195C241.919 23.7721 240.306 23.3984 238.082 23.3984C236.478 23.3984 234.855 23.6081 233.215 24.0273C231.574 24.4466 230.079 25.0299 228.73 25.7773L227.992 25.4492V17.4648C229.906 16.7174 232.076 16.1523 234.5 15.7695C236.943 15.3867 239.395 15.1953 241.855 15.1953C247.306 15.1953 251.289 16.1068 253.805 17.9297C256.32 19.7526 257.578 22.6784 257.578 26.707V36.7969H261.133V45H246.203L244.863 40.707C244.007 42.2018 242.785 43.3776 241.199 44.2344C239.632 45.0729 237.882 45.4922 235.949 45.4922ZM241.664 38.4922C242.156 38.4922 242.667 38.4102 243.195 38.2461C243.742 38.0638 244.161 37.8268 244.453 37.5352V32.9688C243.997 32.7682 243.533 32.6315 243.059 32.5586C242.603 32.4857 242.174 32.4492 241.773 32.4492C240.844 32.4492 240.06 32.7227 239.422 33.2695C238.802 33.8164 238.492 34.5456 238.492 35.457C238.492 36.3867 238.784 37.125 239.367 37.6719C239.951 38.2188 240.716 38.4922 241.664 38.4922ZM264.496 45V36.7969H268.734V24.2461H264.496V16.7812L281.859 15.168V36.7969H286.098V45H264.496ZM275.297 13.3359C272.909 13.3359 270.995 12.7617 269.555 11.6133C268.133 10.4648 267.422 8.94271 267.422 7.04688C267.422 5.15104 268.133 3.65625 269.555 2.5625C270.977 1.45052 272.9 0.894531 275.324 0.894531C277.803 0.894531 279.727 1.43229 281.094 2.50781C282.479 3.58333 283.172 5.09635 283.172 7.04688C283.172 8.97917 282.461 10.5104 281.039 11.6406C279.635 12.7708 277.721 13.3359 275.297 13.3359Z"
          fill="white"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_4150_18268"
          x="0.730469"
          y="0.890625"
          width="289.367"
          height="63.5391"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_4150_18268"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_4150_18268"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};

export default WangcaiIcon;
