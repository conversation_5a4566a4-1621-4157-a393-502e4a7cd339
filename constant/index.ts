import {NETWORK_ENUM} from "./type";
import {IAvatarMetadata} from "../AvatarOrdinalsBrowser/constant/type";

//接口地址
export const REQUEST_URL: string = process.env.REQUEST_URL as string;
//接口地址
export const GAME_REQUEST_URL: string = process.env.GAME_REQUEST_URL as string;
//长连接地址
export const GAME_SOCKET_URL: string = process.env.GAME_SOCKET_URL as string;
//钱包网络
export const APP_NETWORK: NETWORK_ENUM = process.env
  .APP_NETWORK as NETWORK_ENUM;
// avatar版本
export const AVATAR_VERSION: string = process.env.AVATAR_VERSION as string;
// ord浏览器接口地址

export const ORD_SERVER: string = process.env.ORD_SERVER as string;
// nft图浏览器接口地址
export const ORD_NFT_IMG_SERVER: string = process.env
  .ORD_NFT_IMG_SERVER as string;
// 官网展示的默认奔跑中的人物铭文id
export const HOME_RUN_AVATAR_METADATA: IAvatarMetadata = {
  actionId:
    "cee28efb83e383e6ed49fcdae0cc18772d596ac184440cb95f536a72bd9c01bai0",
} as IAvatarMetadata;
//是否主网临时版本
export const IS_PRO_TEMPORARY_VERSION = true;

// turnstile key
export const TURNSTILE_KEY = "0x4AAAAAAA2S2QoS838whqiC";

// 本地获取owner
export const LOCAL_HOSTS_DOMAIN = process.env.LOCAL_HOSTS_DOMAIN;
// 用户域名跳转
export const DOMAIN_JUMP = LOCAL_HOSTS_DOMAIN ? "localhost" : ".uniworlds";
// 用户域名跳转
// 主域名
export const WEBSITE_DOMAIN = process.env.DOMAIN as string;

export const SHARE_DOMAIN = process.env.SHARE_DOMAIN as string;
export const AI_REQUEST_URL = process.env.AI_REQUEST_URL as string;

// 气泡弹窗与人物模型距离的比例
export const POPOVER_HEIGHT = 0.2;

export const ENABLE_BASIC_SUMMARY_NFT = false; // 是否启用基础摘要NFT

// 打字效果速率
export const TYPING_EFFECT_RATE = 30;

// 推特授权轮询状态时间
export const TWITTER_AUTH_TIME = process.env.TWITTER_AUTH_TIME as string || "5000";
// 推特授权轮询上限次数
export const TWITTER_AUTH_MAX_TIMES = process.env
  .TWITTER_AUTH_MAX_TIMES as string || "10";
// 推特授权轮询超时时间
export const TWITTER_AUTH_TIMEOUT = process.env
  .TWITTER_AUTH_TIMEOUT as string || "5 * 60 * 1000";

// 社区名称集合
export const COMMUNITY_NAME_MAP = {
  "potato": "potato",
  "wangcai": "wangcai",
  "TheLonelyBit": "TheLonelyBit",
  "Pizza": "Pizza",
  "DomoDucks": "DomoDucks",
};

