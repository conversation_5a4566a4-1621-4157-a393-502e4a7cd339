
export const SVG_FILE = {
  yesIcon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4.5 11.25L9 17.25L19.5 6.75" stroke="#140F08" stroke-width="2" stroke-linecap="round"/>
    </svg>
    `,
  noIcon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M6 6L18 18" stroke="#140F08" stroke-width="2" stroke-linecap="round"/>
<path d="M18 6L6 18" stroke="#140F08" stroke-width="2" stroke-linecap="round"/>
</svg>
`,
  emptyIcon: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="20" height="20" rx="2" fill="#BB3530"/>
    <rect width="20" height="20" rx="2" fill="black"/>
    <path d="M8.42614 12.6534V12.5682C8.43561 11.6638 8.5303 10.9441 8.71023 10.4091C8.89015 9.87405 9.14583 9.44081 9.47727 9.10938C9.80871 8.77794 10.2064 8.47254 10.6705 8.19318C10.9498 8.02273 11.2008 7.8215 11.4233 7.58949C11.6458 7.35275 11.821 7.08049 11.9489 6.77273C12.0814 6.46496 12.1477 6.12405 12.1477 5.75C12.1477 5.28598 12.0388 4.88352 11.821 4.54261C11.6032 4.2017 11.312 3.93892 10.9474 3.75426C10.5829 3.5696 10.178 3.47727 9.73295 3.47727C9.3447 3.47727 8.97064 3.55777 8.6108 3.71875C8.25095 3.87973 7.95028 4.13305 7.70881 4.47869C7.46733 4.82434 7.32765 5.27652 7.28977 5.83523H5.5C5.53788 5.0303 5.74621 4.34138 6.125 3.76847C6.50852 3.19555 7.01278 2.75758 7.63778 2.45455C8.26752 2.15152 8.96591 2 9.73295 2C10.5663 2 11.2907 2.16572 11.9063 2.49716C12.5265 2.8286 13.0047 3.28314 13.3409 3.86079C13.6818 4.43845 13.8523 5.09659 13.8523 5.83523C13.8523 6.35606 13.7718 6.82718 13.6108 7.24858C13.4545 7.66998 13.2273 8.0464 12.929 8.37784C12.6354 8.70928 12.2803 9.00284 11.8636 9.25852C11.447 9.51894 11.1132 9.79356 10.8622 10.0824C10.6113 10.3665 10.429 10.705 10.3153 11.098C10.2017 11.491 10.1402 11.9811 10.1307 12.5682V12.6534H8.42614ZM9.33523 16.858C8.98485 16.858 8.68419 16.7325 8.43324 16.4815C8.18229 16.2306 8.05682 15.9299 8.05682 15.5795C8.05682 15.2292 8.18229 14.9285 8.43324 14.6776C8.68419 14.4266 8.98485 14.3011 9.33523 14.3011C9.68561 14.3011 9.98627 14.4266 10.2372 14.6776C10.4882 14.9285 10.6136 15.2292 10.6136 15.5795C10.6136 15.8116 10.5545 16.0246 10.4361 16.2188C10.3224 16.4129 10.1686 16.5691 9.97443 16.6875C9.78504 16.8011 9.57197 16.858 9.33523 16.858Z" fill="white"/>
</svg>`,
  actionSelectIcon: `<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.4226 9.9375C18.4226 11.4293 19.0152 12.8601 20.0701 13.915C21.125 14.9699 22.5558 15.5625 24.0476 15.5625C25.5395 15.5625 26.9702 14.9699 28.0251 13.915C29.08 12.8601 29.6726 11.4293 29.6726 9.9375C29.6726 8.44566 29.08 7.01492 28.0251 5.96002C26.9702 4.90513 25.5395 4.3125 24.0476 4.3125C22.5558 4.3125 21.125 4.90513 20.0701 5.96002C19.0152 7.01492 18.4226 8.44566 18.4226 9.9375Z" fill="#FF8316" fill-opacity="0.5" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8.12707 12.3989L16.978 15.142C17.8767 15.4205 18.8122 15.5621 19.753 15.562H28.3246C29.2779 15.562 30.2257 15.4166 31.1352 15.1308L39.853 12.3914C40.964 12.0427 42.2305 12.3914 42.7283 13.4442C43.3986 14.8617 43.393 16.0692 43.2102 16.9355C43.0011 17.9339 42.1396 18.5855 41.2002 18.9858L31.6377 23.062L30.8408 40.1864C30.7593 41.9545 29.4505 43.417 27.6852 43.5492C26.4748 43.6403 25.2615 43.6862 24.0477 43.687C22.6827 43.687 21.4414 43.627 20.4121 43.5492C18.6468 43.418 17.3371 41.9545 17.2555 40.1864L16.4586 23.062L6.85864 19.0308C5.84051 18.6024 4.91801 17.8758 4.74739 16.7845C4.61614 15.9464 4.65176 14.8205 5.22551 13.5127C5.70364 12.4252 6.9927 12.0464 8.12707 12.3989Z" fill="#FF8316" fill-opacity="0.5" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M24.0476 43.6875V31.5V43.6875Z" fill="#FF8316" fill-opacity="0.5"/>
<path d="M24.0476 43.6875V31.5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  actionUnselectIcon:`<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.4226 9.9375C18.4226 11.4293 19.0152 12.8601 20.0701 13.915C21.125 14.9699 22.5558 15.5625 24.0476 15.5625C25.5395 15.5625 26.9702 14.9699 28.0251 13.915C29.08 12.8601 29.6726 11.4293 29.6726 9.9375C29.6726 8.44566 29.08 7.01492 28.0251 5.96002C26.9702 4.90513 25.5395 4.3125 24.0476 4.3125C22.5558 4.3125 21.125 4.90513 20.0701 5.96002C19.0152 7.01492 18.4226 8.44566 18.4226 9.9375Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8.12707 12.3999L16.978 15.143C17.8767 15.4215 18.8122 15.5631 19.753 15.563H28.3246C29.2779 15.563 30.2257 15.4176 31.1352 15.1318L39.853 12.3924C40.9639 12.0436 42.2305 12.3924 42.7283 13.4452C43.3986 14.8627 43.393 16.0702 43.2102 16.9365C43.0011 17.9349 42.1396 18.5865 41.2002 18.9868L31.6377 23.063L30.8408 40.1874C30.7593 41.9555 29.4505 43.418 27.6852 43.5502C26.4748 43.6412 25.2615 43.6872 24.0477 43.688C22.6827 43.688 21.4414 43.628 20.4121 43.5502C18.6468 43.419 17.3371 41.9555 17.2555 40.1874L16.4586 23.063L6.85864 19.0318C5.84051 18.6033 4.91801 17.8768 4.74739 16.7855C4.61614 15.9474 4.65176 14.8215 5.22551 13.5136C5.70364 12.4261 6.9927 12.0474 8.12707 12.3999Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M24.0476 43.6875V31.5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  kingSelectIcon:`<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.3832 30.8638L8.72705 14.5728L16.6955 21.3607L23.9999 10.5L31.3043 21.3607L39.2728 14.5728L36.6166 30.8638L36.591 31.0355C38.1447 31.5193 39.2728 32.9688 39.2728 34.6817C39.2728 36.7905 37.5633 38.5 35.4545 38.5H12.5453C10.4365 38.5 8.72705 36.7905 8.72705 34.6817C8.72705 32.9688 9.85506 31.5193 11.4088 31.0355L11.3832 30.8638Z" fill="#FF8316" fill-opacity="0.5"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M24.8297 9.94192C24.6439 9.66567 24.3329 9.5 24 9.5C23.667 9.5 23.356 9.66567 23.1702 9.94192L16.4911 19.8729L9.37556 13.8115C9.0563 13.5396 8.60125 13.4957 8.23593 13.7017C7.87061 13.9077 7.67264 14.3198 7.74013 14.7337L10.3963 31.0247C10.4203 31.1723 10.4755 31.307 10.5541 31.4232C9.45825 32.0943 8.7271 33.3026 8.7271 34.6817C8.7271 36.7905 10.4366 38.5 12.5453 38.5H35.4546C37.5633 38.5 39.2728 36.7905 39.2728 34.6817C39.2728 33.3026 38.5417 32.0943 37.4458 31.4232C37.5244 31.307 37.5796 31.1723 37.6036 31.0247L40.2598 14.7337C40.3273 14.3198 40.1293 13.9077 39.764 13.7017C39.3986 13.4957 38.9436 13.5396 38.6243 13.8115L31.5088 19.8729L24.8297 9.94192ZM35.6165 30.8669C35.6164 30.8128 35.6207 30.758 35.6297 30.7029L37.8475 17.1006L31.9528 22.1219C31.7359 22.3067 31.4505 22.3902 31.1682 22.3514C30.8859 22.3126 30.6336 22.1552 30.4746 21.9188L24 12.2919L17.5253 21.9188C17.3663 22.1552 17.114 22.3126 16.8317 22.3514C16.5494 22.3902 16.264 22.3067 16.0471 22.1219L10.1524 17.1006L12.3702 30.7029C12.3792 30.758 12.3835 30.8128 12.3834 30.8669C12.4371 30.8647 12.4911 30.8635 12.5453 30.8635H35.4546C35.5088 30.8635 35.5628 30.8647 35.6165 30.8669ZM10.7271 34.6817C10.7271 33.6776 11.5411 32.8635 12.5453 32.8635H35.4546C36.4588 32.8635 37.2728 33.6776 37.2728 34.6817C37.2728 35.6859 36.4588 36.5 35.4546 36.5H12.5453C11.5411 36.5 10.7271 35.6859 10.7271 34.6817Z" fill="white"/>
</svg>
`,
  kingUnselectIcon:`<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M24.6223 9.95644C24.4829 9.74926 24.2496 9.625 23.9999 9.625C23.7503 9.625 23.5169 9.74926 23.3776 9.95644L16.5422 20.1198L9.21343 13.8768C8.97399 13.6729 8.6327 13.64 8.35871 13.7945C8.08472 13.949 7.93624 14.258 7.98686 14.5685L10.643 30.8595C10.6622 30.9769 10.7076 31.0835 10.7724 31.1742C9.55642 31.8129 8.72708 33.088 8.72708 34.5567C8.72708 36.6655 10.4366 38.375 12.5453 38.375H35.4546C37.5633 38.375 39.2728 36.6655 39.2728 34.5567C39.2728 33.088 38.4435 31.8129 37.2275 31.1742C37.2923 31.0835 37.3377 30.9769 37.3569 30.8595L40.013 14.5685C40.0636 14.258 39.9152 13.949 39.6412 13.7945C39.3672 13.64 39.0259 13.6729 38.7864 13.8768L31.4577 20.1198L24.6223 9.95644ZM35.8668 30.7605C35.8655 30.7137 35.8686 30.6661 35.8764 30.6181L38.2038 16.3436L31.7907 21.8066C31.628 21.9452 31.4139 22.0078 31.2022 21.9787C30.9905 21.9496 30.8013 21.8316 30.682 21.6543L23.9999 11.7189L17.3179 21.6543C17.1986 21.8316 17.0094 21.9496 16.7976 21.9787C16.5859 22.0078 16.3719 21.9452 16.2092 21.8066L9.7961 16.3436L12.1235 30.6181C12.1313 30.6661 12.1344 30.7137 12.1331 30.7605C12.2685 30.746 12.406 30.7385 12.5453 30.7385H35.4546C35.5939 30.7385 35.7314 30.746 35.8668 30.7605ZM10.2271 34.5567C10.2271 33.2764 11.265 32.2385 12.5453 32.2385H35.4546C36.7349 32.2385 37.7728 33.2764 37.7728 34.5567C37.7728 35.8371 36.7349 36.875 35.4546 36.875H12.5453C11.265 36.875 10.2271 35.8371 10.2271 34.5567Z" fill="white"/>
</svg>
`,
  petSelectIcon:`<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21.6405 14.1802C21.9831 16.8669 20.4514 18.9338 18.6496 19.1636C16.8478 19.3933 14.8465 17.7769 14.5039 15.0902C14.1614 12.4034 15.6931 10.3365 17.4949 10.1068C19.2967 9.87708 21.298 11.4935 21.6405 14.1802Z" fill="#FF8316" fill-opacity="0.5" stroke="white" stroke-width="2"/>
<path d="M13.283 21.7312C14.527 24.137 13.7979 26.6041 12.1845 27.4384C10.571 28.2727 8.13644 27.4415 6.89239 25.0357C5.64834 22.6298 6.37742 20.1628 7.99088 19.3284C9.60433 18.4941 12.0389 19.3253 13.283 21.7312Z" fill="#FF8316" fill-opacity="0.5" stroke="white" stroke-width="2"/>
<path d="M26.2418 14.1802C25.8992 16.8669 27.4309 18.9338 29.2327 19.1636C31.0345 19.3933 33.0358 17.7769 33.3784 15.0902C33.721 12.4034 32.1893 10.3365 30.3875 10.1068C28.5857 9.87708 26.5843 11.4935 26.2418 14.1802Z" fill="#FF8316" fill-opacity="0.5" stroke="white" stroke-width="2"/>
<path d="M34.7173 21.7312C33.4732 24.137 34.2023 26.6041 35.8158 27.4384C37.4292 28.2727 39.8638 27.4415 41.1079 25.0357C42.3519 22.6298 41.6228 20.1628 40.0094 19.3284C38.3959 18.4941 35.9613 19.3253 34.7173 21.7312Z" fill="#FF8316" fill-opacity="0.5" stroke="white" stroke-width="2"/>
<path d="M33 32.5615C33 34.0466 32.1294 35.4988 30.5028 36.6221C28.8803 37.7427 26.5843 38.4678 24 38.4678C21.4157 38.4678 19.1197 37.7427 17.4972 36.6221C15.8706 35.4988 15 34.0466 15 32.5615C15 30.9779 15.9682 28.6805 17.6781 26.7467C19.3745 24.8281 21.6234 23.4678 24 23.4678C26.3766 23.4678 28.6255 24.8281 30.3219 26.7467C32.0318 28.6805 33 30.9779 33 32.5615Z" fill="#FF8316" fill-opacity="0.5" stroke="white" stroke-width="2"/>
</svg>
`,
  petUnselectIcon: `<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21.8885 14.1486C22.2426 16.9258 20.6622 19.159 18.6812 19.4115C16.7002 19.6641 14.61 17.899 14.2559 15.1218C13.9018 12.3445 15.4822 10.1114 17.4632 9.85882C19.4442 9.60624 21.5344 11.3714 21.8885 14.1486Z" stroke="white" stroke-width="1.5"/>
<path d="M13.505 21.6163C14.791 24.1033 14.0732 26.7432 12.2993 27.6605C10.5254 28.5778 7.95629 27.6374 6.67032 25.1505C5.38436 22.6636 6.10213 20.0237 7.87605 19.1064C9.64996 18.1891 12.2191 19.1294 13.505 21.6163Z" stroke="white" stroke-width="1.5"/>
<path d="M25.9938 14.1486C25.6397 16.9258 27.2201 19.159 29.2011 19.4115C31.1821 19.6641 33.2723 17.899 33.6264 15.1218C33.9805 12.3445 32.4001 10.1114 30.4191 9.85882C28.4381 9.60624 26.3479 11.3714 25.9938 14.1486Z" stroke="white" stroke-width="1.5"/>
<path d="M34.4952 21.6163C33.2092 24.1033 33.927 26.7432 35.7009 27.6605C37.4748 28.5778 40.044 27.6374 41.3299 25.1505C42.6159 22.6636 41.8981 20.0237 40.1242 19.1064C38.3503 18.1891 35.7812 19.1294 34.4952 21.6163Z" stroke="white" stroke-width="1.5"/>
<path d="M33.25 32.5615C33.25 34.1521 32.3172 35.6729 30.6449 36.8279C28.9756 37.9807 26.6286 38.7178 24 38.7178C21.3714 38.7178 19.0244 37.9807 17.3551 36.8279C15.6828 35.6729 14.75 34.1521 14.75 32.5615C14.75 30.897 15.756 28.5431 17.4908 26.5811C19.2155 24.6305 21.5272 23.2178 24 23.2178C26.4728 23.2178 28.7845 24.6305 30.5092 26.5811C32.244 28.5431 33.25 30.897 33.25 32.5615Z" stroke="white" stroke-width="1.5"/>
</svg>

`,
  shirtSelectIcon: `<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.9714 8.11719H15.1604C14.2109 8.11719 12.154 10.6889 11.5357 11.3047C10.6302 12.2102 9.79706 13.1468 8.99502 14.0626C8.18522 14.9837 8.07655 16.3782 8.79839 17.3691C9.06488 17.7391 9.35465 18.1013 9.68581 18.4299C10.0144 18.7585 10.374 19.0508 10.7466 19.3173C11.7349 20.0366 13.132 19.9279 14.0531 19.1207C14.1222 19.0612 14.207 19.0228 14.2974 19.0101C14.3877 18.9975 14.4798 19.011 14.5627 19.0492C14.6455 19.0873 14.7157 19.1485 14.7648 19.2254C14.8139 19.3023 14.8398 19.3917 14.8396 19.4829C14.7775 25.1179 14.7697 31.7153 14.9689 36.4655C14.9842 36.9468 15.1442 37.4124 15.428 37.8015C15.7119 38.1905 16.1064 38.485 16.5601 38.6465C21.6052 40.3929 26.3631 40.1989 31.3513 38.6207C31.8272 38.4666 32.244 38.1696 32.545 37.77C32.8459 37.3704 33.0163 36.8878 33.033 36.3879C33.2296 31.6455 33.2244 25.0868 33.1624 19.4829C33.1614 19.3917 33.1869 19.3021 33.2356 19.225C33.2844 19.148 33.3544 19.0866 33.4372 19.0483C33.52 19.0101 33.6121 18.9965 33.7024 19.0094C33.7927 19.0222 33.8774 19.0609 33.9463 19.1207C34.8673 19.9305 36.2619 20.0392 37.2528 19.3199C37.6292 19.0516 37.9839 18.754 38.3135 18.4299C38.6447 18.1013 38.9345 17.7417 39.2035 17.3691C39.9228 16.3782 39.8141 14.9837 39.0069 14.0626C38.2049 13.1468 37.3692 12.2102 36.4637 11.3047C35.8531 10.6941 33.8014 8.11719 32.8389 8.11719H28.4614C28.2278 9.20222 27.6295 10.1744 26.766 10.8718C25.9026 11.5692 24.8263 11.9496 23.7164 11.9496C22.6065 11.9496 21.5301 11.5692 20.6667 10.8718C19.8033 10.1744 19.2049 9.20222 18.9714 8.11719Z" fill="#FF8316" fill-opacity="0.5" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  shirtUnselectIcon: `<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.9714 8.11816H15.1604C14.2109 8.11816 12.154 10.6899 11.5357 11.3056C10.6302 12.2112 9.79706 13.1477 8.99502 14.0636C8.18522 14.9847 8.07655 16.3792 8.79839 17.3701C9.06488 17.7401 9.35465 18.1023 9.68581 18.4309C10.0144 18.7594 10.374 19.0518 10.7466 19.3183C11.7349 20.0375 13.132 19.9289 14.0531 19.1217C14.1222 19.0622 14.207 19.0238 14.2974 19.0111C14.3877 18.9984 14.4798 19.012 14.5627 19.0501C14.6455 19.0883 14.7157 19.1495 14.7648 19.2264C14.8139 19.3033 14.8398 19.3926 14.8396 19.4839C14.7775 25.1189 14.7697 31.7163 14.9689 36.4665C14.9842 36.9478 15.1442 37.4134 15.428 37.8025C15.7119 38.1915 16.1064 38.486 16.5601 38.6475C21.6052 40.3939 26.3631 40.1998 31.3513 38.6216C31.8272 38.4676 32.244 38.1705 32.545 37.771C32.8459 37.3714 33.0163 36.8888 33.033 36.3889C33.2296 31.6465 33.2244 25.0878 33.1624 19.4839C33.1614 19.3927 33.1869 19.3031 33.2356 19.226C33.2844 19.1489 33.3544 19.0876 33.4372 19.0493C33.52 19.011 33.6121 18.9975 33.7024 19.0103C33.7927 19.0232 33.8774 19.0618 33.9463 19.1217C34.8673 19.9315 36.2619 20.0401 37.2528 19.3209C37.6292 19.0525 37.9839 18.755 38.3135 18.4309C38.6447 18.1023 38.9345 17.7427 39.2035 17.3701C39.9228 16.3792 39.8141 14.9847 39.0069 14.0636C38.2049 13.1477 37.3692 12.2112 36.4637 11.3056C35.8531 10.695 33.8014 8.11816 32.8389 8.11816H28.4614C28.2278 9.2032 27.6295 10.1754 26.766 10.8728C25.9026 11.5701 24.8262 11.9505 23.7164 11.9505C22.6065 11.9505 21.5301 11.5701 20.6667 10.8728C19.8033 10.1754 19.2049 9.2032 18.9714 8.11816Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  menuEmptyIcon: `<svg width="128" height="101" viewBox="0 0 128 101" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g opacity="0.5">
        <path d="M21.118 44.6646L29.1439 50.7606M22.0831 51.7256L28.1791 43.6997" stroke="url(#paint0_linear_1128_1878)" stroke-width="3.63636" stroke-linecap="round"/>
        <circle cx="105.899" cy="46.4852" r="3.48485" stroke="url(#paint1_linear_1128_1878)" stroke-width="2.72727"/>
        <circle cx="39.8379" cy="31.9391" r="2.87879" stroke="url(#paint2_linear_1128_1878)" stroke-width="1.51515"/>
        <circle cx="74.2324" cy="12.3939" r="1.36364" fill="#CAC8C7"/>
        <circle cx="106.202" cy="65.8785" r="4.54545" fill="#837C73"/>
        <circle cx="28.3233" cy="36.4851" r="2.72727" fill="#837C73"/>
        <circle cx="85.2934" cy="15.5757" r="4.54545" fill="#837C73"/>
        <g filter="url(#filter0_d_1128_1878)">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M90.2017 17.5545C90.0005 16.8287 89.2491 16.4035 88.5234 16.6046C87.7976 16.8058 87.3724 17.5572 87.5735 18.283L88.0373 19.9563L86.3642 20.4201C85.6384 20.6212 85.2132 21.3726 85.4143 22.0984C85.6155 22.8242 86.3669 23.2494 87.0927 23.0483L88.7658 22.5845L89.2297 24.2581C89.4308 24.9838 90.1823 25.4091 90.908 25.2079C91.6338 25.0068 92.059 24.2553 91.8579 23.5296L91.394 21.856L93.0678 21.3921C93.7935 21.1909 94.2188 20.4395 94.0176 19.7138C93.8164 18.988 93.065 18.5627 92.3393 18.7639L90.6655 19.2278L90.2017 17.5545Z" fill="url(#paint3_linear_1128_1878)"/>
        </g>
        <ellipse cx="65.2939" cy="55.5756" rx="33.3333" ry="32.4242" fill="url(#paint4_linear_1128_1878)"/>
        <path d="M32.7329 72.6906C34.9859 71.1934 34.8574 68.7076 34.5116 67.6518C38.2172 61.8932 59.2653 66.932 66.2319 67.2199C73.1985 67.5079 73.7914 64.6285 81.3509 62.757C88.9104 60.8854 96.9146 64.4846 96.4699 67.6518C96.1141 70.1856 97.4086 71.5869 98.1004 71.9708C99.3356 72.7866 101.628 75.0805 100.917 77.7294C100.027 81.0406 94.0983 81.9044 95.1359 82.4803C96.1734 83.0562 97.9521 85.0717 96.4699 87.6631C94.9876 90.2545 88.6265 89.8179 86.9835 89.5346C86.1381 89.3889 78.2382 87.6631 73.7914 88.6708C69.3446 89.6786 57.3384 89.8226 52.7434 87.6631C48.1484 85.5036 43.1087 89.3907 39.996 86.5113C37.5058 84.2079 40.3418 81.8085 42.0711 80.8967C39.9466 80.8487 34.9563 80.4072 31.9918 79.0251C28.2861 77.2975 29.9166 74.5622 32.7329 72.6906Z" fill="#CFCFCF"/>
        <path opacity="0.6" d="M93.4746 70.986C82.8371 84.3492 48.1716 72.4445 34.8383 69.0607C24.7777 44.2122 51.808 28.4042 66.6565 30.1213C93.4746 33.2228 104.535 57.0911 93.4746 70.986Z" fill="url(#paint5_linear_1128_1878)"/>
        <g filter="url(#filter1_d_1128_1878)">
            <path d="M54.1575 63.1514C54.7916 67.592 61.5793 76.5354 72.5725 74.1891" stroke="#837C73" stroke-width="4.84848" stroke-linecap="round"/>
        </g>
        <g filter="url(#filter2_d_1128_1878)">
            <path d="M55.7247 39.9351L58.9882 51.7088" stroke="#837C73" stroke-width="4.84848" stroke-linecap="round"/>
            <path d="M51.4706 47.4536L63.2444 44.1902" stroke="#837C73" stroke-width="4.84848" stroke-linecap="round"/>
        </g>
        <g filter="url(#filter3_d_1128_1878)">
            <path d="M79.9661 57.2075L83.2295 68.9813" stroke="#837C73" stroke-width="4.84848" stroke-linecap="round"/>
            <path d="M75.7119 64.7261L87.4857 61.4626" stroke="#837C73" stroke-width="4.84848" stroke-linecap="round"/>
        </g>
        <path d="M43.1725 78.3025C35.7786 79.5146 31.4049 76.9893 30.1422 75.5752C28.1725 79.9691 39.3846 80.7267 42.1119 80.8782C44.8392 81.0297 51.3543 76.9612 43.1725 78.3025Z" fill="url(#paint6_linear_1128_1878)"/>
        <path opacity="0.7" d="M49.0809 84.818C43.99 87.2423 40.394 85.8281 39.2324 84.818C39.2324 86.9393 43.0203 88.6059 46.0506 87.3938C48.4748 86.4241 51.2021 86.9897 52.2627 87.3938C59.2324 91.4847 74.687 88.6059 76.9597 88.3029C79.2324 87.9998 85.293 89.9695 90.8991 89.9695C95.8991 89.9695 98.0203 84.818 96.5051 86.4847C94.99 88.1513 88.7779 89.0604 84.2324 87.3938C80.5961 86.0604 77.8688 86.2321 76.9597 86.4847C66.6567 88.9089 56.9092 86.8887 53.3233 85.5756C51.6264 84.3635 49.788 84.5655 49.0809 84.818Z" fill="url(#paint7_linear_1128_1878)"/>
        <path d="M93.9295 81.3328C92.5962 81.8176 93.98 82.2419 94.8386 82.3934C94.5356 82.3157 95.0166 82.5897 97.869 80.8782C101.657 78.6055 101.101 76.9155 100.748 75.5752C100.899 78.3025 95.5962 80.7267 93.9295 81.3328Z" fill="url(#paint8_linear_1128_1878)"/>
    </g>
    <defs>
        <filter id="filter0_d_1128_1878" x="84.1524" y="16.5547" width="11.1272" height="11.1274" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.21212"/>
            <feGaussianBlur stdDeviation="0.606061"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1128_1878"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1128_1878" result="shape"/>
        </filter>
        <filter id="filter1_d_1128_1878" x="51.7329" y="60.7271" width="25.6885" height="19.9088" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dx="1.21212" dy="2.42424"/>
            <feGaussianBlur stdDeviation="0.606061"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1128_1878"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1128_1878" result="shape"/>
        </filter>
        <filter id="filter2_d_1128_1878" x="47.8337" y="37.5103" width="19.0477" height="19.0478" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.21212"/>
            <feGaussianBlur stdDeviation="0.606061"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1128_1878"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1128_1878" result="shape"/>
        </filter>
        <filter id="filter3_d_1128_1878" x="72.075" y="54.7827" width="19.0477" height="19.0478" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.21212"/>
            <feGaussianBlur stdDeviation="0.606061"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1128_1878"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1128_1878" result="shape"/>
        </filter>
        <linearGradient id="paint0_linear_1128_1878" x1="21.884" y1="51.5739" x2="27.9801" y2="43.548" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FF8A00"/>
            <stop offset="1" stop-color="#FFB06A"/>
        </linearGradient>
        <linearGradient id="paint1_linear_1128_1878" x1="105.899" y1="41.6367" x2="105.899" y2="51.3337" gradientUnits="userSpaceOnUse">
            <stop stop-color="#F9B34C"/>
            <stop offset="1" stop-color="#FF7E20"/>
        </linearGradient>
        <linearGradient id="paint2_linear_1128_1878" x1="39.8379" y1="28.3027" x2="39.8379" y2="35.5755" gradientUnits="userSpaceOnUse">
            <stop stop-color="#F9B34C"/>
            <stop offset="1" stop-color="#FF8F3E"/>
        </linearGradient>
        <linearGradient id="paint3_linear_1128_1878" x1="88.5237" y1="16.6046" x2="90.9083" y2="25.2078" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FF8A00"/>
            <stop offset="1" stop-color="#FF9142"/>
        </linearGradient>
        <linearGradient id="paint4_linear_1128_1878" x1="65.2939" y1="23.1514" x2="74.8895" y2="72.5453" gradientUnits="userSpaceOnUse">
            <stop stop-color="#ECECEC"/>
            <stop offset="0.684045" stop-color="#CECECE"/>
        </linearGradient>
        <linearGradient id="paint5_linear_1128_1878" x1="65.1104" y1="44.6668" x2="65.1104" y2="74.9455" gradientUnits="userSpaceOnUse">
            <stop stop-color="#F3F3F3" stop-opacity="0"/>
            <stop offset="1" stop-color="#F3F3F3"/>
        </linearGradient>
        <linearGradient id="paint6_linear_1128_1878" x1="38.5137" y1="77.2254" x2="38.5137" y2="80.6308" gradientUnits="userSpaceOnUse">
            <stop stop-color="#989898" stop-opacity="0"/>
            <stop offset="1" stop-color="#A5A5A5"/>
        </linearGradient>
        <linearGradient id="paint7_linear_1128_1878" x1="61.8082" y1="85.8786" x2="97.1112" y2="86.1816" gradientUnits="userSpaceOnUse">
            <stop stop-color="#AEAEAE"/>
            <stop offset="0.832676" stop-color="#818181" stop-opacity="0.553704"/>
            <stop offset="1" stop-color="#F3F3F3" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="paint8_linear_1128_1878" x1="97.1789" y1="77.6953" x2="97.1789" y2="82.0702" gradientUnits="userSpaceOnUse">
            <stop stop-color="#989898" stop-opacity="0"/>
            <stop offset="1" stop-color="#A5A5A5"/>
        </linearGradient>
    </defs>
</svg>
`,
  setIcon: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M17.448 3.6269C15.1965 5.87832 15.2562 7.05154 16.3522 8.14748C17.4481 9.2434 18.6213 9.30314 20.8728 7.05171C20.9615 6.96299 21.1106 6.98026 21.1714 7.08994C22.293 9.11345 22.034 11.0884 20.3689 12.7534C18.8061 14.3163 16.9702 14.6404 15.0774 13.7468C15.0077 13.7139 14.9241 13.7319 14.8728 13.7895C14.175 14.5715 12.607 16.2152 11.83 16.9922C11.0531 17.7691 9.39095 19.3558 8.60885 20.0535C7.67712 20.8848 6.22617 21.0634 5.25025 20.2842C4.8724 19.9825 4.51716 19.6272 4.21542 19.2494C3.43631 18.2735 3.61488 16.8226 4.44614 15.8908C5.14386 15.1087 6.73046 13.4465 7.50743 12.6696C8.2844 11.8926 9.9281 10.3246 10.7101 9.62688C10.7677 9.57554 10.7858 9.492 10.7528 9.42225C9.85924 7.52946 10.1833 5.69358 11.7462 4.13069C13.4113 2.46562 15.3862 2.20667 17.4097 3.32821C17.5194 3.38903 17.5367 3.53817 17.448 3.6269Z" fill="#686663"/>
</svg>
`,
  eyesSvg: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21.919 10.7996C22.183 11.1287 22.3291 11.5563 22.3291 11.9997C22.3291 12.4431 22.183 12.8707 21.919 13.1998C20.2473 15.2238 16.442 19.1644 11.9999 19.1644C7.55783 19.1644 3.75261 15.2238 2.08085 13.1998C1.81681 12.8707 1.67065 12.4431 1.67065 11.9997C1.67065 11.5563 1.81681 11.1287 2.08085 10.7996C3.75261 8.77556 7.55783 4.83496 11.9999 4.83496C16.442 4.83496 20.2473 8.77556 21.919 10.7996Z" stroke="#686663" stroke-width="1.71" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12 15.184C13.7586 15.184 15.1842 13.7583 15.1842 11.9997C15.1842 10.2411 13.7586 8.81543 12 8.81543C10.2413 8.81543 8.81567 10.2411 8.81567 11.9997C8.81567 13.7583 10.2413 15.184 12 15.184Z" stroke="#686663" stroke-width="1.71" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  arrowSvg:`<svg width="10" height="17" viewBox="0 0 10 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_1372_1518)">
<path d="M0 0.5L7.31672 4.15836C8.96121 4.9806 10 6.6614 10 8.5C10 10.3386 8.96121 12.0194 7.31672 12.8416L0 16.5V0.5Z" fill="#FAFAFA" fill-opacity="0.6"/>
</g>
<defs>
<filter id="filter0_b_1372_1518" x="-8" y="-7.5" width="26" height="32" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1372_1518"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1372_1518" result="shape"/>
</filter>
</defs>
</svg>
`,
  eyesCloseSvg: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.52893 7.55176C5.07844 10.4314 8.29014 12.4097 12.0009 12.4097C15.7116 12.4097 18.9233 10.4314 20.4728 7.55176" stroke="#686663" stroke-width="1.71" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5.23195 9.76465L1.65112 12.7656" stroke="#686663" stroke-width="1.71" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.47822 12.1055L7.75403 16.4478" stroke="#686663" stroke-width="1.71" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M18.7681 9.76465L22.349 12.7656" stroke="#686663" stroke-width="1.71" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14.5159 12.1055L16.2401 16.4478" stroke="#686663" stroke-width="1.71" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,

}
