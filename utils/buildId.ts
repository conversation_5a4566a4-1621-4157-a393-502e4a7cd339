function getBuildId() {
  const buildId = process.env.NEXT_PUBLIC_BUILD_ID || Date.now().toString();
  // 记录当前构建版本到控制台
  console.log("Current build version:", buildId);

  const storedBuildId = localStorage.getItem("app_build_id");
  if (storedBuildId && storedBuildId !== buildId) {
    console.log(
      "New version detected! Previous:",
      storedBuildId,
      "Current:",
      buildId
    );
  }
  localStorage.setItem("app_build_id", buildId);
}

export default getBuildId;
