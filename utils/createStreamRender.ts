/**
 * 读取器类型
 */
type ReaderType = ReadableStreamDefaultReader<Uint8Array<ArrayBufferLike>>;

/**
 * 创建流渲染的属性接口
 * @template T 消息处理的数据类型
 */
interface CreateStreamRenderProps<T = StreamMessage> {
  reader: ReaderType;
  decoder: TextDecoder;
  options: StreamRenderOptions<T>;
}

/**
 * 推流消息类型定义
 */
export type StreamMessageType = "content" | "citationFiles";

/**
 * 推流消息内容类型
 */
export interface StreamMessage {
  type: StreamMessageType;
  content: any;
}

/**
 * 引用文件类型
 */
export interface CitationFile {
  _id: string;
  fileId: string;
  fileUrl: string;
  title: string;
  webLink: string;
  url?: string;
}

/**
 * 流渲染选项接口
 */
export interface StreamRenderOptions<T = StreamMessage> {
  onMessage: (data: T) => void;
  onContent?: (content: string) => void;
  onCitationFiles?: (files: CitationFile[]) => void;
  onError?: (error: Error) => void;
  onComplete?: () => void;
  dataPrefix?: string;
  dataTransformer?: (data: string) => T;
}

/**
 * 通用的流式读取处理函数
 * @template T 消息处理的数据类型
 * @param reader 可读流的读取器
 * @param decoder 文本解码器
 * @param options 配置选项
 * @returns 一个包含终止方法的控制器对象
 */
export function createStreamRender<T = StreamMessage>(
  props: CreateStreamRenderProps<T>
) {
  const { reader, decoder, options } = props;
  const {
    onMessage,
    onContent,
    onCitationFiles,
    onError,
    onComplete,
    dataPrefix = "data: ",
    dataTransformer = (data: string) => JSON.parse(data) as unknown as T,
  } = options;

  let buffer = "";
  let isAborted = false;

  const controller = new AbortController();
  controller.signal.addEventListener("abort", () => {
    isAborted = true;
  });

  function processStream() {
    if (isAborted) {
      return;
    }

    reader
      .read()
      .then(({ done, value }) => {
        if (done) {
          // 处理缓冲区中剩余的数据
          if (buffer) {
            processBuffer();
          }

          if (onComplete) onComplete();
          return;
        }

        // 解码并处理数据
        const text = decoder.decode(value, { stream: true });
        buffer += text;

        // 检查是否是完整的JSON响应（非SSE格式）
        if (buffer.trim().startsWith("{") && buffer.trim().endsWith("}")) {
          try {
            const jsonData = JSON.parse(buffer.trim());

            // 检查是否是错误响应
            if (jsonData.code !== undefined && jsonData.code !== 1) {
              if (onError) {
                onError(new Error(jsonData.msg || "Unknown error"));
              }
              return; // 停止处理
            }
          } catch (error) {
            // 不是完整的JSON或解析失败，继续正常处理
          }
        }

        // 处理按行分割的SSE数据
        const lines = buffer.split("\n");
        buffer = lines.pop() || ""; // 保留最后一行（可能不完整）

        for (const line of lines) {
          processLine(line);
        }

        // 继续读取
        processStream();
      })
      .catch((error) => {
        console.log(
          "Stream processing stopped:",
          isAborted ? "Aborted by user" : error
        );
        if (!isAborted && onError)
          onError(error instanceof Error ? error : new Error(String(error)));
      });
  }

  function processBuffer() {
    // 按行分割缓冲区
    const lines = buffer.split("\n");

    // 保留最后一行（可能不完整）
    buffer = lines.pop() || "";

    // 处理完整的行
    for (const line of lines) {
      processLine(line);
    }
  }

  function processLine(line: string) {
    // 忽略空行
    if (!line.trim()) return;

    // 检查是否是SSE数据行
    if (line.startsWith(dataPrefix)) {
      const data = line.slice(dataPrefix.length);

      // 尝试转换数据
      let parsedData: any = null;
      if (dataTransformer) {
        try {
          parsedData = dataTransformer(data);

          // 检查转换后的数据是否包含错误码
          if (
            parsedData &&
            parsedData.code !== undefined &&
            parsedData.code !== 1
          ) {
            onContent?.(parsedData.msg);
            return; // 不处理此消息
          }
        } catch (error) {
          // console.warn("Error transforming data:", error);
          return;
        }
      } else {
        parsedData = data;
      }

      // 如果数据为null，跳过处理
      if (parsedData === null) return;

      // 处理不同类型的消息
      if (parsedData.type === "content" && onContent) {
        onContent(parsedData.content || "");
      } else if (parsedData.type === "citationFiles" && onCitationFiles) {
        onCitationFiles(parsedData.content as CitationFile[]);
      }

      // 调用通用消息处理器
      onMessage(parsedData);
    } else {
      // 尝试解析非SSE格式的JSON
      try {
        const jsonData = JSON.parse(line);

        // 检查是否是错误响应
        if (jsonData.code !== undefined && jsonData.code !== 1) {
          if (onError) {
            onError(new Error(jsonData.msg || "Unknown error"));
          }
          return; // 不处理此消息
        }

        // 处理正常JSON消息
        onMessage(jsonData);
      } catch (error) {
        // 不是JSON，忽略或作为普通文本处理
        // console.warn("Received non-JSON line:", line);
      }
    }
  }

  // 开始处理流
  processStream();

  return controller;
}
