import * as THREE from 'three';
import AvatarObject from "./Avatar/AvatarObject";
import {OrbitControls} from 'three/examples/jsm/controls/OrbitControls.js';

const lightDebugOn = false;

export default class AvatarRenderer {
    // @ts-ignore
    private domElement: HTMLElement;
    private scene: THREE.Scene;
    private renderer: THREE.WebGLRenderer;
    private controls: OrbitControls | undefined;
    private camera: THREE.PerspectiveCamera;
    private didInit: boolean = false;
    private light: THREE.DirectionalLight | undefined;
    private avatarObject: AvatarObject | undefined;
    private isHomePage: boolean = false
    private isDestroy: boolean = false

    constructor(domElement: HTMLElement) {
        this.domElement = domElement;

        // Create scene
        this.scene = new THREE.Scene();

        // Create renderer
        this.renderer = new THREE.WebGLRenderer({alpha: true, antialias: true, preserveDrawingBuffer: true});
        this.renderer.setClearColor(0x000000, 0);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        this.camera = new THREE.PerspectiveCamera(60, 1, 0.1, 30);

    }


    init(transparentBackground: boolean) {
        if (this.didInit) {
            throw new Error("Already initialized!");
        }

        if (transparentBackground) {
            this.isHomePage = true
        }

        // Create camera
        window.addEventListener('resize', this._initCamera.bind(this), false);
        this._initCamera();

        // Add renderer to DOM, replacing its contents
        this.domElement.innerHTML = "";
        this.domElement.appendChild(this.renderer.domElement);

        this.camera.position.set(1, 1.5, 2);
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.minDistance = 1.5;
        this.controls.maxDistance = 8;
        this.controls.zoomSpeed = 2;
        this.controls.rotateSpeed = 1.5;
        this.controls.maxPolarAngle = Math.PI / 3 * 2;
        this.controls.enablePan = false;
        this.controls.enableDamping = true;
        this.controls.target.set(0, 0.75, 0);
        if (this.isHomePage) {
            this.controls.minDistance = 1.3;
            this.controls.maxDistance = 1.3;
            this.controls.maxPolarAngle = Math.PI * 0.45;
            this.controls.minPolarAngle = Math.PI * 0.45;
            this.controls.target.set(0, 1.15, 0);
            this.camera.position.set(-1.8, 1.35, 2);
        }

        if (transparentBackground) {
            this.renderer.setClearColor(0x000000, 0);  // 0x000000 是黑色，第二个参数设置透明度为 0（完全透明）
        } else {
            this._initScene()
        }

        // Start render loop
        this._spawnDirectionalLight();

        this.didInit = true;

        this._renderLoop();
    }

    _initCamera() {
        const aspectRatio = this.domElement.clientWidth / this.domElement.clientHeight;

        if (!this.camera) {
            this.camera = new THREE.PerspectiveCamera(60, aspectRatio, 0.1, 30);
        } else {
            this.camera.aspect = aspectRatio;
        }

        if (this.renderer) {
            this.renderer.setSize(this.domElement.clientWidth, this.domElement.clientHeight);
        }

        this.camera.updateProjectionMatrix();
    }

    _initScene() {
        //创建地面
        const floorGeometry = new THREE.PlaneGeometry(50, 50);
        const floorMaterial = new THREE.MeshStandardMaterial({color: 0xffffff});
        const floorMesh = new THREE.Mesh(floorGeometry, floorMaterial);
        floorMesh.receiveShadow = true
        floorMesh.position.set(0, 0, 0);
        floorMesh.rotation.x = -Math.PI / 2;
        this.scene.add(floorMesh);


        //创建背景
        this.scene.background = new THREE.Color(0xcccccc);
        //创建雾
        this.scene.fog = new THREE.Fog(0xcccccc, 1, 20); // 雾的颜色，近处的雾浓度，远处的雾浓度

        // 创建一个平面网格
        const gridHelper = new THREE.GridHelper(50, 50);
        this.scene.add(gridHelper);
    }


    _spawnDirectionalLight() {
        const ambientLight = new THREE.AmbientLight(0xffffff, 1);
        this.scene.add(ambientLight);

        this.light = new THREE.DirectionalLight(0xffffff, 2);
        this.light.position.set(-5, 10, 8);

        this.light.castShadow = true;
        this.scene.add(this.light);

        const target = new THREE.Object3D();
        target.position.set(0, 1, 0); // 目标位置
        this.scene.add(target);
        this.light.target = target;
        this.light.shadow.bias = -0.00001;
        this.light.shadow.normalBias = 0.01;
        this.light.shadow.mapSize.width = 2048;
        this.light.shadow.mapSize.height = 2048;
        //
        if (lightDebugOn) {
            const helper = new THREE.CameraHelper(this.light.shadow.camera);
            this.scene.add(helper);
        }
    }

    // -----------------------------------------------------------------------------------------------------------------
    // Render loop

    _renderLoop() {
        if (this.isDestroy) {
            return;
        }
        requestAnimationFrame(this._renderLoop.bind(this));

        this._update();
        this._render();
    }

    _update() {
        if (this.controls) {
            this.controls.update();
        }

        if (this.avatarObject) {
            if (this.isHomePage) {
                this.avatarObject._updateAnimation(0.8)
            } else {
                this.avatarObject._updateAnimation()
            }
        }
    }

    _render() {
        this.renderer.render(this.scene, this.camera);
    }

    // -----------------------------------------------------------------------------------------------------------------
    // Public API

    setAvatarObject(avatarObject: AvatarObject) {
        this.avatarObject = avatarObject;
        this.scene.add(this.avatarObject.sceneGroup)
    }

    destroy() {
        this.isDestroy = true;
    }
}
