import AvatarPartsModel from "./Avatar/AvatarPartsModel";
import {IFatherInscription, IInitDefaultOptions} from "../constant/type";
import AvatarData from "./Avatar/Data/AvatarData";
import AvatarMain from "./AvatarMain";

/**
 * Public API for BeatSaberAvatar.js.
 *
 * @package BeatSaberAvatar.js
 * @type {Window.BeatSaberAvatar}
 * @link https://github.com/roydejong/BeatSaberAvatar.js
 */
export const BeatSaberAvatar = class {
    static setup(domElement: HTMLElement | null, globalParts: IFatherInscription[], avatarData: AvatarData | null = null, options: IInitDefaultOptions) {
        // Init avatar part library
        AvatarPartsModel.init(globalParts);

        // Create and return renderer instance
        let renderInstance = new AvatarMain();
        renderInstance.init(options, domElement);
        renderInstance.setAvatarData(avatarData || null);
        return {
            renderInstance,
            avatarPartsModel: AvatarPartsModel
        }
    }
};
