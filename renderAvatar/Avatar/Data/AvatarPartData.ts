export default class AvatarPartData {
    meshId: string | null;
    textureId: string | null;
    color: string | null;
    isChange: boolean;

    constructor() {
        /**
         * A custom skin color, overrides normally available skin color choices. This is not possible in game.
         */
        this.meshId = null;
        this.textureId = null;
        this.color = null;
        this.isChange = false;
    }
}
