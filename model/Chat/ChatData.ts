import {generateUUID} from "three/src/math/MathUtils";

export class ChatData {

  uuid: string
  playerId: string
  content: string
  replyTo: string
  timestamp: number
  isTg: boolean

  constructor() {
    this.uuid = generateUUID()
    this.playerId = ''
    this.content = ''
    this.replyTo = ''
    this.timestamp = Date.now()
    this.isTg = false
  }

  encode() {
    return {
      uuid: this.uuid,
      playerId: this.playerId,
      content: this.content,
      replyTo: this.replyTo,
      timestamp: this.timestamp,
      isTg: this.isTg
    }
  }

  decode(data: any) {
    this.uuid = data.uuid
    this.playerId = data.playerId
    this.content = data.content
    this.replyTo = data.replyTo
    this.timestamp = data.timestamp
    this.isTg = data.isTg
  }

}
