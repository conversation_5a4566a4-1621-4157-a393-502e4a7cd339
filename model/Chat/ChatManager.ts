import {ChatData} from "@/model/Chat/ChatData";
import {ChatTabType} from "@/model/Chat/ChatType";
import {ChatDataList} from "@/model/Chat/ChatDataList";
import {GetGameNetWork} from "@/world/hooks/useNetWork";
import {AppGameApiKey, GetMyPlayer} from "@/world/Character/MyPlayer";
import {ChatListener} from "@/model/Chat/ChatListener";
import {ChatEvent} from "@/model/Chat/ChatEvent";
import {game} from "@/world/Proto/generated/game_messages";

export class ChatManager {
  private static instance: ChatManager
  private dataListMap: Map<ChatTabType, ChatDataList> = new Map()
  private intervalId: any
  private isRunning = false

  private constructor() {
  }

  static getInstance() {
    if (!ChatManager.instance) {
      ChatManager.instance = new ChatManager()
    }
    return ChatManager.instance
  }

  clientStart() {
    this.intervalId = setInterval(() => {
      this.onTick()
    }, 1000)

    const gameNetWork = GetGameNetWork()
    gameNetWork.watchRoomStatus((data) => {
      if (data.isEnterRoom) {
        gameNetWork.sendChatMsg(ChatTabType.Room, game.C2SPacketType.C2S_CHAT_ENTER, {toJSON: () => ({})} as any)
      } else {
        this.outChatType(ChatTabType.Room)
      }
    })
  }

  clientStop() {
    clearInterval(this.intervalId)
  }

  getChatTypes() {
    const list = Array.from(this.dataListMap.keys())
    list.sort((a, b) => a - b)
    return list
  }

  getChatList(tabType: ChatTabType) {
    return this.dataListMap.get(tabType)
  }

  addChatMessage(tabType: ChatTabType, data: {
    uuid: string,
    playerId: string,
    content: string,
    replyTo: string,
    timestamp: number
  }) {
    const chatData = new ChatData()
    chatData.decode(data)
    const dataList = this.dataListMap.get(tabType)
    if (dataList) {
      dataList.addChatMessage(chatData)
    }
    ChatListener.getInstance().notifyListener(ChatEvent.ReceiveChat, tabType)
  }

  enterChatType(tabType: ChatTabType) {
    if (this.dataListMap.has(tabType)) {
      return
    }
    this.dataListMap.set(tabType, new ChatDataList(tabType))
    ChatListener.getInstance().notifyListener(ChatEvent.ChatTypeChange, this.getChatTypes())
    ChatListener.getInstance().notifyListener(ChatEvent.ReceiveChat, tabType)
  }

  outChatType(tabType: ChatTabType) {
    this.dataListMap.delete(tabType)
    ChatListener.getInstance().notifyListener(ChatEvent.ChatTypeChange, this.getChatTypes())
  }

  sendChatMessage(playerId: string, tabType: ChatTabType, content: string, replyTo: string) {
    const chatData = new ChatData()
    chatData.playerId = playerId
    chatData.content = content
    chatData.replyTo = replyTo
    const data = chatData.encode()
    this.addChatMessage(tabType, data)
    const gameNetWork = GetGameNetWork()
    gameNetWork.sendChatMsg(tabType, game.C2SPacketType.C2S_CHAT_MESSAGE, game.ChatMessage.create(data))
    if (tabType === ChatTabType.Room) {
      const myPlayer = GetMyPlayer()
      myPlayer.callAppApi(AppGameApiKey.sendChat, content);
    }
  }

  onTick() {
    const gameNetWork = GetGameNetWork()
    if (gameNetWork.isConnected()) {
      if (!this.isRunning) {
        this.isRunning = true
        gameNetWork.sendChatMsg(ChatTabType.SatWorld, game.C2SPacketType.C2S_CHAT_ENTER, {toJSON: () => ({})} as any)
        gameNetWork.sendChatMsg(ChatTabType.Fractal, game.C2SPacketType.C2S_CHAT_ENTER, {toJSON: () => ({})} as any)
        gameNetWork.sendChatMsg(ChatTabType.SatWorld, game.C2SPacketType.C2S_CHAT_MESSAGE, game.ChatMessage.create({
          uuid: "1",
          playerId: "1",
          content: "Hello World",
          replyTo: "",
          timestamp: Date.now(),
          tabType: ChatTabType.SatWorld
        }))
      }
    } else {
      if (this.isRunning) {
        this.isRunning = false
        this.dataListMap.clear()
        ChatListener.getInstance().notifyListener(ChatEvent.ChatTypeChange, this.getChatTypes())
      }
    }
  }
}