import {ChatTabType} from "@/model/Chat/ChatType";
import {ChatData} from "@/model/Chat/ChatData";

export class ChatDataList {

  tabType: ChatTabType
  private chatDataList: ChatData[] = []
  private chatDataMap: Map<string, ChatData> = new Map()
  private newMessageCount = 0
  private lookingIndex = 0

  constructor(type: ChatTabType) {
    this.tabType = type
  }

  addChatMessage(chatData: ChatData) {
    this.chatDataList.push(chatData)
    this.chatDataMap.set(chatData.uuid, chatData)
    this.newMessageCount++
  }

  getNewMessageCount() {
    return this.newMessageCount
  }

  clearNewMessageCount() {
    this.newMessageCount = 0
  }

  getChatDataList() {
    return this.chatDataList
  }
}
