import React, {useCallback, useEffect, useRef, useState} from "react";
import {useTwitterAuth} from "@/hooks/useTwitterAuth";
import {useDispatch, useSelector} from "react-redux";
import {IAppState} from "@/constant/type";
import toast from "react-hot-toast";
import CloudFlareModal from "../../CloudFlareModal";
import {AppGameApiKey, GetMyPlayer} from "@/world/Character/MyPlayer";
import EventRules from "@/components/EventRules";
import RulesContent from "@/components/EventRules/components/RulesContent";
import PizzaRulesContent from "@/components/EventRules/components/PizzaRulesContent";
import {useComboManager} from "@/components/Combo/ComboManager";
import useRewards from "@/components/ChatRoom/components/Reward";
import useHandItem from "@/hooks/useHandItem";
import AnimationSequence, {AnimationSequenceRef} from "@/components/AnimationSequence";
import {DecryptedData<PERSON><PERSON>, useFish} from "@/hooks/useFish";
import {useResourceList} from "@/hooks/useResourceList";
import useConnectWallet from "@/hooks/useConnectWallet";
import {resetGameState} from "@/store/game";
import {setLoaderType} from "@/store/app";
import {LoadingPageType} from "@/world/Config/DoorConfig";
import SynthesisSystem, {SynthesisSystemRef} from "@/components/SynthesisSystem";
import {pickUpDropItem} from "@/server";
import {ItemDropConfig, ItemDropData} from "@/world/Config/ItemDropConfig";
import {Events} from "@/utils/clientEvents";

const openCloudFlare = true

const GameApiWindow: React.FC = () => {
  const myPlayer = GetMyPlayer();
  // 添加显示/隐藏状态
  const dispatch = useDispatch();
  // 添加按钮加载状态
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const cf_callbackRef = useRef<(token: string) => void>((token: string) => {
  })
  const {Rewards, openRewards} = useRewards();
  const [isOpenRule, setIsOpenRule] = useState(false);
  const [isOpenPizzaRule, setIsOpenPizzaRule] = useState(false);

  const {reportScorePickAxe, onReceiveTool} = useHandItem();
  useResourceList({autoFetch: true});
  const {onGetFishing, onSetFishScore, onCompleteFishEggTask} = useFish();
  const {showComboWithType, ComboDisplay} = useComboManager();
  const {btcAddress, dogEasterEgg, randomEventResult} = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const {disconnectWallet} = useConnectWallet();
  const {cutTree} = useHandItem();

  const synthesisSystemRef = useRef<SynthesisSystemRef>(null);
  const animationSequenceRef = useRef<AnimationSequenceRef>(null);

  const {startAuth} = useTwitterAuth({
    onSuccess: (data) => {
      // console.log("Twitter connected successfully:", data);
    },
    onError: (error) => {
      // console.error("Twitter connection failed:", error);
    },
  });

  useEffect(() => {
    if (randomEventResult) {
      setQuantity(randomEventResult.quantity);
      setIsOpen(true);
    }
  }, [randomEventResult]);

  // 处理Twitter认证
  const handleTwitterAuth = useCallback(async () => {
    try {
      await startAuth();
    } catch (error) {
      console.error("Twitter auth error:", error);
    }
  }, [startAuth]);

  // 打开领取装备 CloudFlare
  const OpenCloudFlare = useCallback(async (callback: (token: string) => void) => {
    if (!btcAddress) {
      toast.error("Please connect your wallet");
      return;
    }
    cf_callbackRef.current = callback
    setIsModalOpen(true);
  }, [btcAddress]);


  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.activityRule, (ruleType: number) => {
        switch (ruleType) {
          case 1:
            setIsOpenRule(true);
            break;
          case 2:
            setIsOpenPizzaRule(true);
            break;
          default:
            setIsOpenRule(true);
            break
        }
      });
      myPlayer.setAppApi(AppGameApiKey.showCombo, (combo: number) => {
        showComboWithType(combo);
      });
      myPlayer.setAppApi(AppGameApiKey.setLoaderType, (type: LoadingPageType) => {
        dispatch(setLoaderType(type));
      });
      myPlayer.setAppApi(AppGameApiKey.openSynthesis, (sureCallback: () => void) => {
        synthesisSystemRef.current?.open(sureCallback);
      });
      myPlayer.setAppApi(AppGameApiKey.pickUpDrop, async (dropData: ItemDropData) => {
        const res = await pickUpDropItem(String(dropData.id));
        const {code, msg, data} = res.data;
        if (code === 1) {
          // toast.success("拾取掉落物成功");
          ItemDropConfig.getInstance().updateData([data])
          //临时代码
          if ((data.dropItemTag).startsWith('101')) {
            Events.emitItemCollected("/image/t2-1.png", 1);
          } else if ((data.dropItemTag).startsWith('102')) {
            Events.emitItemCollected("/image/t2-2.png", 1);
          } else if ((data.dropItemTag).startsWith('103')) {
            Events.emitItemCollected("/image/t2-3.png", 1);
          }
        } else {
          dropData.isPickedUp = false
          console.error(msg);
        }
      });
    }
  }, []);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.startDogEgg, () => {
        if (dogEasterEgg) {
          animationSequenceRef.current?.start();
        }
      });
    }
  }, [dogEasterEgg]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.showRewards, (name: string, quantity: string) => {
        openRewards({name, quantity});
      });
    }
  }, [openRewards]);
  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.mining, (treeId: string, userItemId: string) => {
        reportScorePickAxe(treeId, userItemId).then().catch(console.error);
      });
    }
  }, [reportScorePickAxe]);
  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.authTwitter, () => {
        handleTwitterAuth().then();
      });
    }
  }, [handleTwitterAuth]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(
        AppGameApiKey.cutTree,
        (treeId: string, userItemId: string) => {
          cutTree(treeId, userItemId).then().catch(console.error);
        }
      );
    }
  }, [cutTree]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.receiveAxe, () => {
        if (openCloudFlare) {
          OpenCloudFlare((token: string) => onReceiveTool(token, 'Axe')).then();
        } else {
          onReceiveTool('123', 'Axe').then();
        }
      });
      myPlayer.setAppApi(AppGameApiKey.receivePickaxe, () => {
        if (openCloudFlare) {
          OpenCloudFlare((token: string) => onReceiveTool(token, 'Pickaxe')).then();
        } else {
          onReceiveTool('123', 'Pickaxe').then();
        }
      });
      myPlayer.setAppApi(AppGameApiKey.receiveFishingRod, () => {
        if (openCloudFlare) {
          OpenCloudFlare((token: string) => onReceiveTool(token, 'FishingPole')).then();
        } else {
          onReceiveTool('123', 'FishingPole').then();
        }
      });
      myPlayer.setAppApi(AppGameApiKey.receiveTool, () => {
        if (openCloudFlare) {
          OpenCloudFlare((token: string) => onReceiveTool(token, '')).then();
        } else {
          onReceiveTool('123', '').then();
        }
      });
    }
  }, [onReceiveTool, OpenCloudFlare]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.useFishingRod, (callback: (json: DecryptedDataJson) => void) => {
        onGetFishing(myPlayer.axeParams?.userItemId || '').then((json) => {
          callback(json as DecryptedDataJson);
        });
      });
    }
  }, [onGetFishing]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.fishingSuccess, () => {
        onSetFishScore().then();
      });
    }
  }, [onSetFishScore]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.disconnectWallet, () => {
        disconnectWallet();
        dispatch(resetGameState());
      });
    }
  }, [disconnectWallet]);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.finishFishEgg, () => {
      });
    }
  }, [onCompleteFishEggTask]);

  return (
    <>
      <CloudFlareModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onVerify={cf_callbackRef.current}
        title="Claim"
        confirmText="Claim Tool"
        cancelText="Cancel"
        description="Do you confirm to claim a tool?"
      />
      <EventRules isOpen={isOpenRule} onClose={() => setIsOpenRule(false)}>
        <RulesContent onClose={() => setIsOpenRule(false)}/>
      </EventRules>
      <EventRules isOpen={isOpenPizzaRule} onClose={() => setIsOpenPizzaRule(false)}>
        <PizzaRulesContent onClose={() => setIsOpenPizzaRule(false)}/>
      </EventRules>
      {/* 连击Combo */}
      {ComboDisplay && <ComboDisplay/>}
      {Rewards && <Rewards/>}
      <AnimationSequence
        ref={animationSequenceRef}
        animationConfigs={dogEasterEgg as any}
      />
      <SynthesisSystem ref={synthesisSystemRef} onClose={() => {
      }}/>
    </>
  );
};

export default GameApiWindow;
