import styled from "styled-components";

// 设置蒙层
export const Mask = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10;
`;

export const PizzaActivityWindowView = styled.div`
  font-family: JetBrainsMono;
  display: flex;
  position: fixed;
  justify-content: center;
  align-items: center;
  //height: 100vh;
  top: 5%;
  left: 40%;

  .pizza-rush {
    width: 412px;
    height: 230px;
    font-size: 20px;
    position: relative;
    color: #542d00;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    box-sizing: border-box;
    padding-top: 60px;
    gap: 5px;
    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url("/image/pizza/rush.png") no-repeat center center;
      background-size: contain;
      z-index: -1;
    }
  }

  .next-round {
    width: 412px;
    height: 230px;
    font-size: 20px;
    position: relative;
    color: #542d00;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    box-sizing: border-box;
    padding-top: 60px;
    gap: 5px;
    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url("/image/pizza/next-round.png") no-repeat center center;
      background-size: contain;
      z-index: -1;
    }
  }

  .time-left {
    width: 412px;
    height: 230px;
    font-size: 20px;
    position: relative;
    color: #542d00;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    box-sizing: border-box;
    padding-top: 60px;
    gap: 5px;
    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url("/image/pizza/time-left.png") no-repeat center center;
      background-size: contain;
      z-index: -1;
    }
  }

  .start {
    &::after {
      background: url("/image/pizza/start-1.png") no-repeat center center;
      background-size: contain;
      z-index: 11;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      content: "";
      width: 360px;
      height: 360px;
    }

    &::before {
      content: "";
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 10;
    }
  }

  .time-up {
    &::after {
      background: url("/image/pizza/time-up.png") no-repeat center center;
      background-size: contain;
      z-index: 11;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      content: "";
      width: 360px;
      height: 360px;
    }

    &::before {
      content: "";
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 10;
    }
  }

  .tallying-up {
    width: 412px;
    height: 200px;
    background: url("/image/pizza/tallying-up.png") no-repeat center center;
    background-size: contain;
    z-index: 11;
  }
`;
