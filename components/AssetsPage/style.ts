import styled from "styled-components";

export const AssetsPageView = styled.div`
  background: linear-gradient(180deg, #CCCCCC 0%, #E7E7E7 100%),
  radial-gradient(29.17% 41.57% at 50% 100%, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%),
  linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2));
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 0 20px;
  box-sizing: border-box;

  .assets-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 0;

    & > .tabs {
      margin-top: 24px;

      & > div {
        width: 160px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 16px;
        background: #ffffff;

        font-family: JetBrainsMono;
        font-size: 18px;
        font-weight: 400;
        line-height: 21.78px;
        color: #140F08;
        cursor: pointer;
      }
    }

    .avatar-card {
      margin-top: 16px;
      border-radius: 32px;
      border: 1px solid #9A9A9A;
      padding: 16px;
      box-sizing: border-box;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
      grid-column-gap: 12px;
      grid-row-gap: 24px;
      background: #FFFFFF;
    }
    .empty-view,.loading-view{
      margin-top: 16px;
      border-radius: 32px;
      border: 1px solid #9A9A9A;
      padding: 16px;
      box-sizing: border-box;
      height: 712px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      &>img{
        width: 200px;
        height: 200px;
      }
      &>p{
        margin: 16px 0 0 0;
        font-family: JetBrainsMono;
        font-size: 20px;
        font-weight: 400;
        line-height: 24.2px;
        text-align: center;
        color: #615A57;
      }
      &>a{
        margin-top: 16px;
        width: 336px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        background: #FF8316;

        font-family: JetBrainsMonoBold;;
        font-size: 20px;
        font-weight: 700;
        line-height: 20px;
        color: #FFFFFF;
      }
    }
  }
`
export const AvatarItemView = styled.div`
  border-radius: 16px;
  background: #F4F4F4;
  padding: 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  cursor: pointer;

  & > .preview-img {
    width: 100%;
    //height: 208px;
    //border: 1px solid #939393;
    //display: flex;
    //align-items: center;
    //justify-content: center;

    & > img {
      width: 100%;
      border-radius: 12px;
      border: 1px solid #939393;
      box-sizing: border-box;
    }
  }

  & > .avatar-item-info {
    padding: 8px;
    box-sizing: border-box;

    & > h2 {
      font-family: JetBrainsMonoBold;
      font-size: 18px;
      font-weight: 700;
      line-height: 21.78px;
      text-align: left;
      color: #FF8316;
      margin: 0;
    }

    & > p {
      font-family: JetBrainsMono;
      font-size: 16px;
      font-weight: 400;
      line-height: 19.36px;
      text-align: left;
      color: #140F08;
      margin: 4px 0 0 0;
      // 超出3行显示省略号
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
    }
  }
`
