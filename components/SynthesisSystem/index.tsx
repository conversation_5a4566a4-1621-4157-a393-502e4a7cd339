import Dialog from "@/commons/Dialog";
import ModalContent from "@/commons/ModalContent";
import {forwardRef, Ref, useImperativeHandle, useRef, useState} from "react";
import Image from "next/image";
import SynthesisContent from "./SynthesisContent";
import SynthesisRecipe from "./SynthesisRecipe";
import useSynthesis from "@/hooks/useSynthesis";
import LocalLoading from "@/components/LoadingContent";

export interface SynthesisSystemRef {
  open: (confirmCallback: () => void) => void;
}

interface ModalProps {
  onClose: () => void;
}

interface IRecipeItem {
  synthesisTag: string;
  itemTag: string;
  itemName?: string;
  itemIcon?: string;
  active?: boolean;
}

const SynthesisSystem = forwardRef<SynthesisSystemRef, ModalProps>(
  ({onClose}: ModalProps, ref: Ref<SynthesisSystemRef>) => {
    const [isOpen, setIsOpen] = useState(false);
    const confirmCallbackRef = useRef<() => void>()

    const {
      loading,
      synthesizing,
      recipes,
      recipesWithStatus,
      selectedRecipe,
      setSelectedRecipe,
      fetchSynthesisList,
      synthesize,
      canSynthesize,
      reset,
    } = useSynthesis();

    useImperativeHandle(ref, () => ({
      open: (confirmCallback: () => void) => {
        // 打开弹窗前获取最新数据
        confirmCallbackRef.current = confirmCallback
        fetchSynthesisList();
        setIsOpen(true);
      },
    }));

    // 处理选择配方
    const handleSelectRecipe = (recipe: IRecipeItem) => {
      // 查找完整的配方信息
      const fullRecipe = recipes.find(
        (item) => item.synthesisTag === recipe.synthesisTag
      );
      if (fullRecipe) {
        setSelectedRecipe(fullRecipe);
      }
    };

    const handleCancel = () => {
      onClose();
      setIsOpen(false);
      // 清空弹窗的数据
      reset();
    };

    // 执行合成操作
    const handleConfirm = async () => {
      if (!selectedRecipe) {
        // toast.error("请先选择一个配方");
        return;
      }

      // 合成前检查材料是否足够
      if (!canSynthesize(selectedRecipe)) {
        // toast.error("材料不足，无法合成");
        return;
      }

      // 执行合成
      const res = await synthesize(selectedRecipe.synthesisTag);

      if (res) {
        // 成功后关闭窗口
        if (confirmCallbackRef.current) {
          confirmCallbackRef.current()
        }
        onClose();
        setIsOpen(false);
        reset();
      }
    };

    return (
      <Dialog
        isOpen={isOpen}
        onClose={() => {
          onClose();
          setIsOpen(false);
        }}
      >
        <div style={{display: "flex", width: "100%", gap: "20px"}}>
          <ModalContent
            modalHeight="488px"
            modalWidth="580px"
            confirmText="Confirm"
            onConfirm={handleConfirm}
            confirmLoading={synthesizing}
            maxHeight="440px"
            cancelText="Cancel"
            onCancel={handleCancel}
            onClose={handleCancel}
            modalBodyPadding="60px 42px 10px"
            footerStyle={{
              justifyContent: "space-evenly",
              gap: "0px",
              padding: "16px 60px",
            }}
            buttonStyle={{
              width: "200px",
            }}
            title={
              <Image
                src="/image/title-bg.png"
                alt="synthesis"
                width={358}
                height={84}
              />
            }
            confirmDisabled={
              !selectedRecipe || !canSynthesize(selectedRecipe) || synthesizing
            }
          >
            {loading ? (
              <div
                style={{
                  textAlign: "center",
                  width: "100%",
                  height: "300px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <LocalLoading/>
              </div>
            ) : selectedRecipe ? (
              <SynthesisContent
                isBatchSynthesis={false}
                currentItem={convertToSynthesisContentFormat(selectedRecipe)}
              />
            ) : (
              <div
                style={{
                  textAlign: "center",
                  width: "100%",
                  height: "300px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                No available formulas
              </div>
            )}
          </ModalContent>
          {/* 右侧配方栏，里面是配方列表，采用双栏布局 两两组合 */}
          <SynthesisRecipe
            loading={loading}
            recipeList={convertToRecipeFormat(recipesWithStatus)}
            onSelectRecipe={handleSelectRecipe}
            selectedRecipeId={selectedRecipe?.synthesisTag}
          />
        </div>
      </Dialog>
    );
  }
);

// 转换数据格式，适配SynthesisRecipe组件
function convertToRecipeFormat(
  recipes: {
    synthesisTag: string;
    itemTag: string;
    itemIcon?: string;
    itemName?: string;
    active: boolean;
  }[]
): IRecipeItem[] {
  return recipes.map((recipe) => ({
    synthesisTag: recipe.synthesisTag,
    itemTag: recipe.itemTag,
    itemName: recipe.itemName || "",
    itemIcon: recipe.itemIcon || "",
    active: recipe.active,
  }));
}

// 转换数据格式，适配SynthesisContent组件
function convertToSynthesisContentFormat(recipe: any) {
  return {
    itemId: recipe.synthesisTag,
    name: recipe.itemName || "",
    description: recipe.description || "",
    icon: recipe.itemIcon || "",
    canSynthesize: recipe.canSynthesize || 0,
    synthetics: recipe.synthesis.map((material: any) => ({
      itemId: material.tag,
      name: material.name || "",
      description: material.description || "",
      icon: material.icon || "",
      currentQuantity: material.currentCount,
      needQuantity: material.count,
      currentDurability: material.currentDurability,
    })),
  };
}

SynthesisSystem.displayName = "SynthesisSystem";

export default SynthesisSystem;
