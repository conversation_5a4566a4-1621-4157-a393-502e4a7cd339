"use client";
import { memo } from "react";
import styled from "styled-components";

interface ProgressProps {
  progress: number;
}

const ProgressContainer = styled.div`
  width: 80%;
  max-width: 400px;
  height: 8px;
  border: 1px solid #000000;
  box-sizing: border-box;
  margin-top: 20px;
  position: relative;
`;

const ProgressText = styled.span`
  position: absolute;
  bottom: 0;
  right: 0;
  transform: translate(50%, 100%);
  color: #000;
  font-family: JetBrainsMono;
  font-size: 20px;
`;

function Progress({ progress }: ProgressProps) {
  return (
    <ProgressContainer>
      <div
        style={{
          width: `${progress}%`,
          height: "100%",
          background: "#000000",
          position: "relative",
        }}
      >
        <ProgressText>{progress}%</ProgressText>
      </div>
    </ProgressContainer>
  );
}

export default memo(Progress);
