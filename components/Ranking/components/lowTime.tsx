import {memo, useEffect, useRef, useState} from "react";
import styled from "styled-components";
import Image from "next/image";
import Tips from "/public/image/tips1.png";
import Tip2 from "/public/image/tip2.png";
import NumberFlow from "@number-flow/react";

const LeftTimeContainer = styled.div<{ isVisible: boolean }>`
  position: relative;
  opacity: ${(props) => (props.isVisible ? 1 : 0)};
  visibility: ${(props) => (props.isVisible ? "visible" : "hidden")};
  transition: opacity 0.3s ease-out, visibility 0.3s ease-out;

  .tip1 {
    color: transparent;
    position: absolute;
    left: calc(100% - 70%);
    z-index: -1;
  }

  .tip2 {
    color: transparent;
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0%);
    z-index: 1;
    top: 10px;
  }

  .tip3 {
    color: transparent;
    position: absolute;
    left: calc(100% - 35%);
    z-index: -1;
  }
`;

const CountdownContainer = styled.div`
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  left: 50%;
  top: 45%;
  z-index: 10;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  font-size: 14px;
  .number-container {
    display: inline-flex;
    align-items: center;
    margin: 0 2px;
  }

  .time-separator {
    margin: 0 2px;
    font-weight: bold;
  }
`;

interface TimeObject {
  hours: number;
  minutes: number;
  seconds: number;
}

interface LowTimeProps {
  downTime?: number; // 时间戳
  onTimeEnd?: () => void;
  label?: string;
}

function LowTime({ downTime, onTimeEnd, label }: LowTimeProps) {
  const [timeRemaining, setTimeRemaining] = useState<TimeObject>({
    hours: 0,
    minutes: 0,
    seconds: 0,
  });
  const [isVisible, setIsVisible] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 计算时分秒
  const calculateTimeRemaining = (targetTimestamp: number): TimeObject => {
    const now = Date.now();
    const diff = Math.max(0, targetTimestamp - now); // 确保不会是负数

    // 计算时分秒
    const totalSeconds = Math.floor(diff / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return { hours, minutes, seconds };
  };

  // 修改显示方式，分别显示小时、分钟和秒的十位和个位
  const getTensAndOnes = (num: number) => {
    const tens = Math.floor(num / 10);
    const ones = num % 10;
    return { tens, ones };
  };

  useEffect(() => {
    if (downTime && downTime > 0) {
      const timeObj = calculateTimeRemaining(downTime);
      setTimeRemaining(timeObj);
      setIsVisible(true);
    }
  }, [downTime]);

  useEffect(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    if (!downTime || downTime <= Date.now()) {
      setIsVisible(false);
      onTimeEnd?.();
      return;
    }

    timerRef.current = setInterval(() => {
      const timeObj = calculateTimeRemaining(downTime);
      setTimeRemaining(timeObj);

      // 检查是否结束
      if (
        timeObj.hours === 0 &&
        timeObj.minutes === 0 &&
        timeObj.seconds === 0
      ) {
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
        setIsVisible(false);
        onTimeEnd?.();
      }
    }, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [downTime, onTimeEnd]);

  // 解构时间为十位和个位数字
  const { tens: hoursTens, ones: hoursOnes } = getTensAndOnes(
    timeRemaining.hours
  );
  const { tens: minutesTens, ones: minutesOnes } = getTensAndOnes(
    timeRemaining.minutes
  );
  const { tens: secondsTens, ones: secondsOnes } = getTensAndOnes(
    timeRemaining.seconds
  );

  return (
    <LeftTimeContainer isVisible={isVisible}>
      <Image
        src={Tip2.src}
        alt="tips"
        width={10}
        height={20}
        className="tip1"
        key="tip1"
      />
      <div className="tip2">
        <Image src={Tips.src} alt="tips" width={260} height={60} />
        <CountdownContainer>
          {label}:
          <div className="number-container">
            {/* 小时 */}
            <NumberFlow value={hoursTens} />
            <NumberFlow value={hoursOnes} />

            <span className="time-separator">:</span>

            {/* 分钟 */}
            <NumberFlow value={minutesTens} />
            <NumberFlow value={minutesOnes} />

            <span className="time-separator">:</span>

            {/* 秒 */}
            <NumberFlow value={secondsTens} />
            <NumberFlow value={secondsOnes} />
          </div>
        </CountdownContainer>
      </div>
      <Image
        src={Tip2.src}
        alt="tips"
        width={10}
        height={20}
        className="tip3"
        key="tip3"
      />
    </LeftTimeContainer>
  );
}

export default memo(LowTime);
