import Image from "next/image";
import CommunityImg from "/public/image/community-title.png";
import CountDownClock from "./CountDownClock";
import ClockImage from "/public/image/time.png";
import FirstRankImg from "/public/image/1-1.png"; // 第一名图标

import { motion } from "motion/react";
import LocalLoading from "@/components/LoadingContent";
import { useMemo } from "react";
import { NpcConfig } from "@/world/Config/NpcConfig";
import * as THREE from "three";
import { AppGameApiKey, GetMyPlayer } from "@/world/Character/MyPlayer";
import { LoadingPageType } from "@/world/Config/DoorConfig";
import GlobalSpaceEvent, {
  CharacterType,
  GlobalDataKey,
} from "@/world/Global/GlobalSpaceEvent";
import { SCENE_TYPE } from "@/constant/type";
import toast from "react-hot-toast";
import {
  COMMUNITY_CONFIG,
  CommunityKey,
  getCommunityAnimationDelay,
  animationConfig,
} from "./communityConfig";
import { CommunityContainer } from "./Community.styles";

interface CommunityProps {
  endTimestamp: number;
  loading?: boolean;
  communityRankList?: any[];
  onClose?: () => void;
}

const Community: React.FC<CommunityProps> = ({
  endTimestamp,
  loading,
  communityRankList = [],
  onClose,
}) => {
  // 处理communityRankList，确保数据按rank排序
  const sortedRankList = useMemo(() => {
    return [...communityRankList].sort((a, b) => a.rank - b.rank);
  }, [communityRankList]);


  const myPlayer = GetMyPlayer();

  /**
   * @param item 点击跳转到对应的npc
   */
  const onClickMenu = (item: any) => {
    const config = COMMUNITY_CONFIG[item.address as CommunityKey];
    const npcId = config?.npcId;
    if (npcId) {
      NpcConfig.getInstance().getData(npcId, (data) => {
        const transformPos = new THREE.Vector3(
          data.transformPosition[0],
          data.transformPosition[1] + 1,
          data.transformPosition[2]
        );
        const direction = transformPos
          .clone()
          .sub(
            new THREE.Vector3(
              data.position[0],
              data.position[1] + 1,
              data.position[2]
            )
          );
        myPlayer.callAppApi(
          AppGameApiKey.setLoaderType,
          LoadingPageType.Default
        );
        GlobalSpaceEvent.SetDataValue(GlobalDataKey.TransformData, {
          characterType: CharacterType.Player,
          position: transformPos,
          sceneType: data.transformMapId as SCENE_TYPE,
          camDirection: direction,
        });
      });
      onClose?.();
    } else {
      toast.error("No npc configured");
    }
  };

  return (
    <CommunityContainer>
      <div className="top">
        <Image
          src={CommunityImg}
          width={400}
          height={100}
          alt="community"
          className="community-title"
        />
        <div className="clock-container">
          <Image
            src={ClockImage}
            alt="Timer"
            priority
            className="clock-image"
            width={48}
            height={48}
          />
          <CountDownClock
            endTimestamp={endTimestamp || 0}
            onCountdownEnd={() => {}}
          />
        </div>
      </div>
      <div className="bottom">
        <div className="bottom-content">
          {loading ? (
            <LocalLoading />
          ) : (
            <>
              {/* 动态生成排名项 */}
              {sortedRankList.map((item, index) => {
                // 获取社区配置
                const config = COMMUNITY_CONFIG[item.address as CommunityKey] || {};
                const communityClass = config.className || "community-thelonelybit";
                const IconComponent = config.icon;
                const avatar = config.avatar;
                // 使用分发方法获取动画延迟
                const rankDelay = getCommunityAnimationDelay(item.rank, "rank");
                const iconDelay = getCommunityAnimationDelay(item.rank, "icon");
                const infoDelay = getCommunityAnimationDelay(item.rank, "info");
                const infoHeight = item.rank === 1 ? 70 : 60;

                return (
                  <motion.div
                    key={item.address}
                    className={`rank-item ${communityClass}`}
                    data-rank={item.rank}
                    initial={{
                      y: animationConfig.card.initialY,
                      opacity: 0,
                      cursor: "pointer",
                    }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{
                      duration: animationConfig.card.duration,
                      delay: rankDelay,
                      type: "spring",
                      stiffness: animationConfig.card.stiffness,
                    }}
                    onClick={() => {
                      onClickMenu(item);
                    }}
                  >
                    {/* 第一名的排名图标使用Image组件 */}
                    {item.rank === 1 ? (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{
                          duration: animationConfig.icon.duration,
                          delay: iconDelay,
                        }}
                        className="rank-image"
                        data-rank={item.rank}
                      >
                        <Image
                          src={FirstRankImg}
                          width={84}
                          height={84}
                          className="first-rank-image"
                          alt={`Rank ${item.rank}`}
                        />
                      </motion.div>
                    ) : item.rank === 2 || item.rank === 3 ? (
                      // 第二、三名的排名图标使用div+背景图片
                      <motion.div
                        className="rank-image"
                        data-rank={item.rank}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{
                          duration: animationConfig.icon.duration,
                          delay: iconDelay,
                        }}
                      />
                    ) : (
                      // 第四、五名的排名图标使用div + 数字
                      <motion.div
                        className="rank-image"
                        data-rank={item.rank}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{
                          duration: animationConfig.icon.duration,
                          delay: iconDelay,
                        }}
                      >
                        <div className="rank-number">{item.rank}</div>
                      </motion.div>
                    )}

                    <motion.div
                      className="rank-info"
                      data-rank={item.rank}
                      initial={{ opacity: 0, x: animationConfig.info.initialX }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{
                        duration: animationConfig.info.duration,
                        delay: infoDelay,
                      }}
                    >
                      <div className="rank-info-image" data-rank={item.rank}>
                        {
                          avatar && (
                            <Image
                              src={avatar}
                              alt={item.address}
                              width={infoHeight}
                              height={infoHeight}
                            />
                          )
                        }
                      </div>
                      <div className="rank-info-text" data-rank={item.rank}>
                        {IconComponent && <IconComponent style={{}} />}
                        <div className="potato-count">
                          {item.score.toLocaleString()}
                        </div>
                      </div>
                    </motion.div>
                  </motion.div>
                );
              })}
            </>
          )}
        </div>
      </div>
    </CommunityContainer>
  );
};

export default Community;
