import { forwardRef, useImperativeHandle, useState, Ref } from "react";
import styled from "styled-components";
import { motion } from "framer-motion";

// 更新Tab的状态类型
export interface TabItem {
  name: string;
  type: string;
  status: "selected" | "expired" | "ongoing" | "upcoming"; // 更新状态名称
}

interface TabsProps {
  tabs: TabItem[];
  onTabChange?: (index: number, tab: TabItem) => void;
  initialActiveIndex?: number;
}

export interface TabsRef {
  setActiveTab: (index: number) => void;
}

// 样式组件
const TabsContainer = styled.div`
  display: flex;
  flex-direction: row;
  gap: 10px;
  padding: 6px 0px;
  position: relative;
  width: fit-content;
  margin: 0 auto;
  width: 100%;
  justify-content: space-around;
`;

const TabButton = styled(motion.button)<{
  $status: string;
  $isActive: boolean;
}>`
  border: none;
  border-radius: 14px; // 圆角胶囊状
  cursor: pointer;
  text-align: center;
  color: #fff;
  min-height: 30px;
  padding: 0px 8px;
  // 增加底部阴影
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.5);
  font-size: 14px;

  /* 根据状态设置不同的背景色 */
  background-color: ${(props) => {
    switch (props.$status) {
      case "selected":
        return "#212121"; // 黑色 - 选中状态
      case "expired":
        return "#E8A44B"; // 浅橙色 - 已过期状态
      case "ongoing":
        return "#FF7043"; // 橙色 - 进行中状态
      case "upcoming":
        return "#A68064"; // 棕色 - 未开始状态
      default:
        return "#E8A44B";
    }
  }};

  /* 选中覆盖其他状态 */
  background-color: ${(props) => (props.$isActive ? "#212121" : "")};

  &:focus {
    outline: none;
  }

`;

const Tabs = forwardRef((props: TabsProps, ref: Ref<TabsRef>) => {
  const { tabs, onTabChange, initialActiveIndex = 0 } = props;
  const [activeTab, setActiveTab] = useState(initialActiveIndex);
  // 暴露setActiveTab方法给父组件
  useImperativeHandle(ref, () => ({
    setActiveTab: (index: number) => {
      if (index >= 0 && index < tabs.length) {
        setActiveTab(index);
        // if (onTabChange) {
        //   onTabChange(index, tabs[index]);
        // }
      }
    },
  }));

  const handleTabClick = (index: number) => {
    setActiveTab(index);
    if (onTabChange) {
      onTabChange(index, tabs[index]);
    }
  };

  return (
    <TabsContainer>
      {tabs.map((tab, index) => (
        <TabButton
          key={index}
          onClick={() => handleTabClick(index)}
          $status={tab.status}
          $isActive={activeTab === index}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {tab.name}
        </TabButton>
      ))}
    </TabsContainer>
  );
});

export default Tabs;
