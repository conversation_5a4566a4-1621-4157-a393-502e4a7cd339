import styled from "styled-components";

export const ChangeSceneWindowView = styled.div`
  font-family: JetBrainsMono;
  display: flex;
  position: fixed;
  left: 32px;
  bottom: 32px;

  .currency-box {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    &>img{
      width: 198px;
      height: 182px;
    }
    &>div{
      width: 176px;
      height: 48px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      background: #140F0899;
      backdrop-filter: blur(8px);
      font-family: JetBrainsMono;
      font-size: 28px;
      font-weight: 500;
      line-height: 28px;
      letter-spacing: -0.04em;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #FFFFFF;
      border-radius: 16px;
      margin-top: -11px;
    }
    &:hover{
      &>div{
        background: #FF8316CC;
      }
    }
  }


`
