import styled from "styled-components";

export const HeaderView = styled.div<{ isFixed: boolean }>`
  position: ${({ isFixed }) => (isFixed ? "absolute" : "relative")};
  left: 0;
  top: 0;
  width: 100%;
  z-index: 2;
  box-sizing: border-box;
  /* padding: 1vw 4vw;  */
  padding: 0 4vw 1vw 4vw;
  .header-view {
    max-width: 1920px;
    margin: 0 auto;
    gap: 1vw;
    /* padding: 22px 64px; */
    box-sizing: border-box;
    display: flex;
    /* align-items: center; */
    align-items: flex-end;
    justify-content: space-between;
    /* gap: 28px; */
    .logo {
      display: flex;
      align-items: center;
      gap: 8px;
      position: relative;
      top: 10px;
      & > img {
        height: 72px;
      }
      & > span {
        font-family: JetBrainsMono;
        font-size: 24px;
        font-weight: 500;
        line-height: 31.68px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #140f08;
      }
    }
    .navs {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 32px;
      .nav-list {
        display: flex;
        align-items: center;
        position: relative;
        .line {
          width: 1px;
          height: 32px;
          background: #140f08;
        }
        .bg-box {
          position: absolute;
          bottom: 0;
          width: 0;
          height: 0;
          border-radius: 24px;
          background: #140f08;
          transition: all 0.3s linear;
          transform: translateX(-50%);
          &.active {
            width: 120px;
            height: 48px;
          }
        }
        .nav-item-box {
          display: flex;
          align-items: center;
        }
        .nav-item {
          width: 144px;
          /* width: 10vw; */
          height: 48px;
          border-radius: 24px;
          background: transparent;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          & > span {
            width: 100%;
            height: 100%;
            font-family: JetBrainsMono;
            font-size: 18px;
            font-weight: 500;
            line-height: 21.78px;
            text-align: center;
            //text-underline-position: from-font;
            //text-decoration-skip-ink: none;
            color: #140f08;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.3s linear;
            cursor: pointer;
          }
          &.active,
          &:hover {
            & > span {
              color: #d1d1d1;
            }
          }
        }
      }
    }
  }
  &:hover {
    z-index: 4;
  }
`;

export const ConnectWalletBtnView = styled.div`
  min-height: 64px;
  padding: 0 42px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  background: #140f08;
  border-radius: 24px;
  cursor: pointer;
  position: relative;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0px 4px 0px 0px #00000040, 0px 4px 0px 0px #ffffff40 inset;

  & > span {
    //styleName: button;
    font-family: JetBrainsMonoBold;
    font-size: 18px;
    font-weight: 700;
    line-height: 21.78px;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #ffffff;
    position: relative;
    z-index: 1;
  }

  & > div.o1 {
    width: 0;
    height: 0;
    position: absolute;
    left: 50%;
    bottom: 0px;
    transform: translate(-50%, 50%);
    background: 24px;
    border-radius: 100%;
    background: rgba(255, 131, 22, 0.47);
    transition: all 0.4s linear;

    & > div {
      width: 0;
      height: 0;
      background: #ff8316;
      transition: all 0.4s linear;
      position: absolute;
      left: 50%;
      border-radius: 50%;
      bottom: -20px;
      transform: translateX(-50%);
    }
  }

  &:hover {
    & > div.o1 {
      width: 300px;
      height: 300px;
      & > div {
        width: 250px;
        height: 250px;
      }
    }
  }
`;
export const ConnectedWalletBtnView = styled.div`
  min-height: 64px;
  /* padding: 0 36px; */
  max-width: 15vw;
  min-width: 11vw;
  padding: 1vw;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  gap: 12px;
  backdrop-filter: blur(4px);
  border-radius: 30px;
  cursor: pointer;
  position: relative;
  background: #fff0c9;
  border: 1px solid #140f08;
  box-shadow: inset 0 0 0 4px #fcdba8;
  .btc-address {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    gap: 6px;
    & > span {
      font-family: JetBrainsMono;
      font-size: 18px;
      line-height: 21.78px;
      font-weight: 400;
      color: #7c7c7c;
    }
    path {
      stroke: #7c7c7c;
    }
  }
  .connected-menu {
    width: calc(100% + 2px);
    position: absolute;
    left: -1px;
    top: 100%;
    z-index: 2;
    background: #fff0c9;
    backdrop-filter: blur(4px);
    padding: 0 12px;
    box-sizing: border-box;
    max-height: 0;
    overflow: hidden;
    border-bottom-right-radius: 30px;
    border-bottom-left-radius: 30px;

    .line {
      width: 60%;
      height: 1px;
      background: #b7b7b7;
      margin: 0 auto;
    }
    .connected-menu-list {
      padding: 18px 0 16px 0;
      box-sizing: border-box;
      .bg-box2 {
        width: calc(100% - 24px);
        height: 0;
        border-radius: 24px;
        position: absolute;
        left: 12px;
        top: 18px;
        background: #140f08;
        transition: transform 0.3s linear;
      }
      .connected-menu-item {
        height: 64px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: JetBrainsMono;
        font-size: 18px;
        font-weight: 400;
        line-height: 21.78px;
        text-align: center;
        color: #140f08;
        text-decoration: none;
        white-space: nowrap;
        border-radius: 24px;
        position: relative;
        transition: all 0.3s linear;

        &.active {
          color: #ffffff;
        }
      }
    }
  }
  &:hover {
    box-shadow: inset 0 4px 0 0 #fcdba8, /* 顶部阴影 */ inset 4px 0 0 0 #fcdba8,
      /* 左侧阴影 */ inset -4px 0 0 0 #fcdba8; /* 右侧阴影 */

    border: 1px solid #140f08;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-bottom: 0;
    .btc-address {
      span {
        color: #140f08;
      }
    }

    .connected-menu {
      max-height: 300px;
      border: 1px solid #140f08;
      border-top: 0;
      overflow: hidden;
      z-index: 10;
      transform: translateZ(0);
      transition: max-height 0.3s;
      box-shadow: inset 4px 0 0 0 #fcdba8, inset -4px 0 0 0 #fcdba8,
        inset 0 -4px 0 0 #fcdba8;
    }
  }
`;
