import styled from "styled-components";
import { useState, useRef, useEffect } from "react";
import ReactDOM from "react-dom";
import Image from "next/image";

interface Option {
  balance: string;
  tick: string;
  anchorAmount?: string;
}

interface TickSelectProps {
  list: Option[];
  onChange: (val: Option) => void;
}

const SelectWrapper = styled.div`
  position: relative;
  display: inline-block;
`;

const SelectButton = styled.button`
  background: #fff7e7;
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  padding: 0;
`;

const Dropdown = styled.div`
  position: absolute;
  top: 56px;
  right: 0;
  min-width: 160px;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.13);
  padding: 10px 0;
  z-index: 100;
  max-height: 180px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0;
  &::-webkit-scrollbar {
    width: 6px;
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: #e5d6c2;
    border-radius: 6px;
  }
`;

const OptionItem = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const OptionRow = styled.div<{ selected: boolean }>`
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 18px 8px 12px;
  cursor: pointer;
  font-size: 18px;
  font-weight: 600;
  color: #14110a;
  background: transparent;
  border-radius: 12px;
  position: relative;
  &:hover {
    background: #f5e5c7;
  }
`;

const TickIcon = "https://fractal-static.unisat.io/icon/brc20/";

const TickSelect = ({ list, onChange }: TickSelectProps) => {
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState<Option>(list[0]);
  const ref = useRef<HTMLDivElement>(null);
  const btnRef = useRef<HTMLButtonElement>(null);
  const [dropdownStyle, setDropdownStyle] = useState<React.CSSProperties>({});

  // 点击外部关闭
  useEffect(() => {
    if (!open) return;
    const handleClick = (e: MouseEvent) => {
      const target = e.target as Node;
      // 判断是否在SelectWrapper或Dropdown内部
      if (
        ref.current &&
        (ref.current.contains(target) ||
          document.querySelector("[data-dropdown]")?.contains(target))
      ) {
        return;
      }
      setOpen(false);
    };
    document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick);
  }, [open]);

  // 若list变化，重置选中项
  useEffect(() => {
    setSelected(list[0]);
  }, [list]);

  // 计算Dropdown绝对位置
  useEffect(() => {
    if (open && btnRef.current) {
      const rect = btnRef.current.getBoundingClientRect();
      setDropdownStyle({
        position: "absolute",
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        zIndex: 9999,
        maxWidth: "220px",
        overflowX: "hidden",
        overflowY: "auto",
      });
    }
  }, [open]);

  const tickUrl = `${TickIcon}${selected.tick}`;

  return (
    <SelectWrapper ref={ref}>
      <SelectButton ref={btnRef} onClick={() => setOpen((v) => !v)}>
        <Image
          src={tickUrl}
          alt={selected.tick}
          width={48}
          height={48}
          style={{ borderRadius: "50%" }}
        />
      </SelectButton>
      {open &&
        ReactDOM.createPortal(
          <Dropdown data-dropdown style={dropdownStyle}>
            {list.map((item) => {
              const tickUrl = `${TickIcon}${item.tick}`;
              return (
                <OptionRow
                  key={item.tick}
                  selected={item.tick === selected.tick}
                  onClick={() => {
                    setSelected(item);
                    onChange(item);
                    setOpen(false);
                  }}
                >
                  <OptionItem>
                    {/* 只有选中才会显示 */}
                    {item.tick === selected.tick ? (
                      <svg
                        width="16"
                        height="13"
                        viewBox="0 0 16 13"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M2 4.88L6.68293 10L14 2"
                          stroke="#FF8316"
                          strokeWidth="3"
                          strokeLinecap="round"
                        />
                      </svg>
                    ) : (
                      <div style={{ width: "16px", height: "29px" }} />
                    )}
                    <Image
                      src={tickUrl}
                      alt={item.tick}
                      width={24}
                      height={24}
                      style={{
                        borderRadius: "50%",
                      }}
                    />
                    <span>{item.tick}</span>
                  </OptionItem>
                </OptionRow>
              );
            })}
          </Dropdown>,
          document.body
        )}
    </SelectWrapper>
  );
};

export default TickSelect;
