import styled from "styled-components";
import Image from "next/image";
import BirthdayImg from "/public/image/chatRoom/birthDay.png";

const ChatRoomBirthdayButtonContainer = styled.div`
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-sizing: border-box;
  margin-right: 4px;
`;

const ChatRoomBirthdayButton = ({ onAirdrop }: { onAirdrop?: () => void }) => {
  return (
    <ChatRoomBirthdayButtonContainer onClick={onAirdrop}>
      <Image src={BirthdayImg.src} alt="birthday" width={40} height={40} />
    </ChatRoomBirthdayButtonContainer>
  );
};

export default ChatRoomBirthdayButton;
