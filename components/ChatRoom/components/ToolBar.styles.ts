import styled from "styled-components";

export const ToolBarContainer = styled.div`
  display: flex;
  align-items: end;
  width: 100%;
  position: absolute;
  top: -65px;
  left: 0;
  box-sizing: border-box;
`;

export const ListContainer = styled.div`
  display: flex;
  align-items: center;
`;

export const ListItem = styled.div<{ active: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ active }) =>
    active ? "rgba(0,0,0,0.8)" : "rgba(0,0,0,0.6)"};
  border: ${({ active }) =>
    active ? "2px solid #fff" : "2px solid transparent"};
  border-radius: 14px;
  height: 54px;
  margin-right: 10px;
  cursor: pointer;
  transition: border 0.2s, background 0.2s, width 0.3s, min-width 0.3s,
    padding 0.3s;
  color: #fff;
  box-shadow: ${({ active }) => (active ? "0 0 8px 0 #fff3" : "none")};
  position: relative;
  min-width: ${({ active }) => (active ? "120px" : "54px")};
  width: ${({ active }) => (active ? "auto" : "54px")};
  padding: ${({ active }) => (active ? "0 18px 0 10px" : "0")};
  overflow: hidden;
  & > .icon-wrapper {
    position: relative;
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  & > .label {
    margin-left: 10px;
    font-size: 20px;
    font-weight: 700;
    color: #fff;
    white-space: nowrap;
    opacity: ${({ active }) => (active ? 1 : 0)};
    transition: opacity 0.2s;
  }
`;

export const CheckboxContainer = styled.label<{ checked: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6);
  width: 140px;
  height: 40px;
  border-radius: 14px;
  padding: 0 10px;
  box-sizing: border-box;
  cursor: pointer;
  margin-left: auto;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  position: relative;
  input[type="checkbox"] {
    display: none;
  }
  &::before {
    content: "";
    display: inline-block;
    width: 22px;
    height: 22px;
    border: 1px solid #fff;
    border-radius: 4px;
    margin-right: 10px;
    background: ${({ checked }) => (checked ? "#fff" : "transparent")};
    box-sizing: border-box;
    transition: background 0.2s;
  }
  &::after {
    content: "";
    display: ${({ checked }) => (checked ? "block" : "none")};
    position: absolute;
    left: 18px;
    top: 9px;
    width: 8px;
    height: 14px;
    border: solid #222;
    border-width: 0 3px 3px 0;
    transform: rotate(45deg);
  }
`;
