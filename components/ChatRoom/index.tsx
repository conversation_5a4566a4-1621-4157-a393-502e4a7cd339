import ChevronLeftIcon from "@/commons/ChevronLeft";
import ChevronRightIcon from "@/commons/ChevronRight";
import {useEffect, useRef, useState} from "react";
import {GetMyPlayer} from "@/world/Character/MyPlayer";
// import { trim } from "lodash";
import {trim} from "es-toolkit";
// @ts-ignore
import CopyToClipboard from "react-copy-to-clipboard";
import PlayerLabel from "./components/PlayerLabel";
import {usePlayerColors} from "@/hooks/usePlayerColors";
import {ChatRoomView, ChevronLeftIconContainer, MessageItem, MessageList, ToggleButton,} from "./ChatRoom.styles";
import ToolBar from "./components/ToolBar";
import ChatRoomInput from "./components/ChatRoomInput";
import ChatRoomReplyMessage, {ReplyMessageRef,} from "./components/ChatRoomReplyMessage";
import ReplyButton from "./components/ReplyButton";
import {ChatTabType} from "@/model/Chat/ChatType";
import {ChatManager} from "@/model/Chat/ChatManager";
import {ChatListener} from "@/model/Chat/ChatListener";
import {ChatEvent} from "@/model/Chat/ChatEvent";
import {ChatData} from "@/model/Chat/ChatData";
import AirdropModal, {AirdropModalRef} from "./components/AirdropModal";
import useRewards from "./components/Reward";

// 定义自己的 Direction 类型
type Direction = "up" | "down" | "left" | "right" | undefined;

interface ChatRoomProps {
  // messages?: ChatMessage[];
  // currentPlayerId?: string;
  useSequentialColors?: boolean;
  onExpandChange?: (isExpanded: boolean) => void;
}

const ChatRoom: React.FC<ChatRoomProps> = ({
  useSequentialColors = false, // 默认使用随机颜色
  onExpandChange,
}) => {
  const [isExpanded, setIsExpanded] = useState(true); // 默认展开
  const [messages, setMessages] = useState<ChatData[]>([]);
  const myPlayer = GetMyPlayer();
  const [copiedMessageIndex, setCopiedMessageIndex] = useState<number | null>(
    null
  ); // 跟踪复制的消息索引
  const messageListRef = useRef<HTMLDivElement>(null); // 消息列表的引用
  const observerRef = useRef<ResizeObserver | null>(null); // ResizeObserver 引用
  const mutationObserverRef = useRef<MutationObserver | null>(null); // MutationObserver 引用
  // 跟踪最后一次消息数量，用于检测新消息
  const lastMessageCountRef = useRef<number>(0);
  // 控制是否显示新消息提示
  const [showNewMessagePrompt, setShowNewMessagePrompt] = useState(false);
  const [typeList, setTypeList] = useState<ChatTabType[]>([]);
  const [curChatType, setCurChatType] = useState<ChatTabType>(ChatTabType.Node);
  const [adminOnly, setAdminOnly] = useState(false);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    index: number | null;
  } | null>(null);
  const [replyInfo, setReplyInfo] = useState<{
    user: string;
    message: string;
  } | null>(null);
  const replyRef = useRef<ReplyMessageRef>(null);
  const airdropModalRef = useRef<AirdropModalRef>(null);
  const { Rewards, openRewards } = useRewards();
  // 使用自定义hook处理玩家颜色
  const { getPlayerColor, getSpecialAccountProps, clearColorMap } =
    usePlayerColors(useSequentialColors);

  // 创建一个基础的滚动信息状态对象
  const [scrollInfo, setScrollInfo] = useState({
    left: 0,
    top: 0,
    right: 0,
    bottom: 0,
    direction: undefined as Direction,
    hasVerticalScroll: false,
    hasHorizontalScroll: false,
    isAtBottom: true,
    isAtTop: true,
    isAtLeft: true,
    isAtRight: false,
    shouldShowNewMessagePrompt: false,
  });

  // 监听 messageListRef.current 的变化，当元素存在时初始化滚动监听
  useEffect(() => {
    const el = messageListRef.current;
    if (!el || !isExpanded) return;

    // console.log("MessageList 元素已挂载，设置滚动监听");

    // 添加滚动事件监听器
    const handleScroll = () => {
      // console.log("滚动事件触发");
      updateScrollInfo();
    };

    el.addEventListener("scroll", handleScroll, { passive: true });

    // 初始化
    updateScrollInfo();

    return () => {
      // console.log("清理滚动事件监听");
      el.removeEventListener("scroll", handleScroll);
    };
  }, [messageListRef.current, isExpanded]);

  // 设置ResizeObserver监听元素大小变化
  useEffect(() => {
    const el = messageListRef.current;
    if (!el || !isExpanded) return;

    // console.log("设置ResizeObserver监听元素大小变化");

    // 创建ResizeObserver监听容器大小变化
    observerRef.current = new ResizeObserver(() => {
      // console.log("检测到大小变化，更新滚动信息");
      // 容器大小变化时更新滚动信息
      requestAnimationFrame(updateScrollInfo);
    });

    observerRef.current.observe(el);

    return () => {
      // console.log("清理ResizeObserver");
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [messageListRef.current, isExpanded]);

  // 设置MutationObserver监听容器内容变化
  useEffect(() => {
    const el = messageListRef.current;
    if (!el || !isExpanded) return;

    // console.log("设置MutationObserver监听内容变化");

    // 创建MutationObserver监听内容变化
    mutationObserverRef.current = new MutationObserver((mutations) => {
      // 过滤出有意义的变化（比如子节点增删、属性变化等）
      const significantChanges = mutations.some(
        (mutation) =>
          mutation.type === "childList" ||
          (mutation.type === "attributes" &&
            ["style", "class"].includes(mutation.attributeName || ""))
      );

      if (significantChanges) {
        // console.log("检测到内容变化，更新滚动信息");
        // 内容变化时更新滚动信息
        requestAnimationFrame(updateScrollInfo);
      }
    });

    // 配置观察选项
    mutationObserverRef.current.observe(el, {
      childList: true, // 观察子节点增删
      subtree: true, // 观察所有后代节点
      attributes: true, // 观察属性变化
      attributeFilter: ["style", "class"], // 只关心样式和类变化
    });

    return () => {
      // console.log("清理MutationObserver");
      if (mutationObserverRef.current) {
        mutationObserverRef.current.disconnect();
      }
    };
  }, [messageListRef.current, isExpanded]);

  // 更新滚动信息的函数
  const updateScrollInfo = () => {
    if (!messageListRef.current) return;

    const el = messageListRef.current;
    const left = el.scrollLeft;
    const top = el.scrollTop;
    const scrollHeight = el.scrollHeight;
    const clientHeight = el.clientHeight;
    const scrollWidth = el.scrollWidth;
    const clientWidth = el.clientWidth;
    const right = scrollWidth - (clientWidth + left);
    const bottom = scrollHeight - (clientHeight + top);

    // 判断是否有滚动条
    const hasVerticalScroll = scrollHeight > clientHeight + 1; // 允许1px误差
    const hasHorizontalScroll = scrollWidth > clientWidth + 1;

    // 判断是否到达边界
    const isAtBottom = hasVerticalScroll && Math.abs(bottom) <= 1;
    const isAtTop = top <= 1;
    const isAtLeft = left <= 1;
    const isAtRight = hasHorizontalScroll && Math.abs(right) <= 1;

    const newInfo = {
      left,
      top,
      right,
      bottom,
      direction: undefined as Direction, // 简化，不计算方向
      hasVerticalScroll,
      hasHorizontalScroll,
      isAtBottom,
      isAtTop,
      isAtLeft,
      isAtRight,
      shouldShowNewMessagePrompt: showNewMessagePrompt, // 使用状态变量
    };

    // 如果滚动到底部，自动隐藏新消息提示
    if (isAtBottom && showNewMessagePrompt) {
      setShowNewMessagePrompt(false);
    }

    // console.log("newInfo=======", newInfo);
    setScrollInfo(newInfo);
  };

  // 处理复制成功回调
  const handleCopySuccess = (text: string, index: number) => {
    // 显示复制成功提示
    setCopiedMessageIndex(index);
    // 1.5秒后重置状态
    setTimeout(() => setCopiedMessageIndex(null), 1500);
  };

  // 通知父组件展开状态变化
  useEffect(() => {
    if (onExpandChange) {
      onExpandChange(isExpanded);
    }
  }, [isExpanded, onExpandChange]);

  // 滚动到底部的函数
  const scrollToBottom = () => {
    if (messageListRef.current) {
      messageListRef.current.scrollTop = messageListRef.current.scrollHeight;
      updateScrollInfo();
    }
  };

  // 初始加载和聊天室展开时滚动到底部
  useEffect(() => {
    if (isExpanded && messageListRef.current) {
      scrollToBottom();
    }
  }, [isExpanded]);

  // 在 messages 变化时，检查是否有新消息
  useEffect(() => {
    // 检测是否有新消息
    if (messages.length > lastMessageCountRef.current) {
      // 如果不在底部，显示新消息提示
      if (scrollInfo.hasVerticalScroll && !scrollInfo.isAtBottom) {
        setShowNewMessagePrompt(true);
      } else {
        // 如果在底部，直接滚动到底部
        scrollToBottom();
      }
    }

    // 更新消息计数
    lastMessageCountRef.current = messages.length;
  }, [messages, scrollInfo]);

  // 监听 scrollInfo 变化，确保"在底部"状态的滚动行为一致
  useEffect(() => {
    // 当滚动位置变化时，如果用户主动滚动到底部，隐藏提示
    if (scrollInfo.isAtBottom) {
      setShowNewMessagePrompt(false);
    }
  }, [scrollInfo]);

  // 使用事件监听更新消息
  useEffect(() => {
    const handleNewMessage = (tabType: ChatTabType) => {
      if (tabType === curChatType) {
        const chatList = ChatManager.getInstance().getChatList(curChatType);
        if (chatList) {
          // 初始加载
          const getChatMessages = chatList.getChatDataList();
          // 过滤掉空消息
          const filteredMessages = getChatMessages.filter(
            (msg) => msg.content && trim(msg.content) !== ""
          );
          setMessages(filteredMessages);
        } else {
          setMessages([]);
        }
      }
    };

    handleNewMessage(curChatType);

    // 添加监听器
    ChatListener.getInstance().addListener(
      ChatEvent.ReceiveChat,
      handleNewMessage
    );

    // 清理监听器
    return () => {
      ChatListener.getInstance().removeListener(
        ChatEvent.ReceiveChat,
        handleNewMessage
      );
    };
  }, [curChatType]);

  useEffect(() => {
    const chatTypeChange = () => {
      setTypeList(ChatManager.getInstance().getChatTypes());
    };
    // 判断是否进入房间
    ChatListener.getInstance().addListener(
      ChatEvent.ChatTypeChange,
      chatTypeChange
    );
    return () => {
      ChatListener.getInstance().removeListener(
        ChatEvent.ChatTypeChange,
        chatTypeChange
      );
    };
  }, []);

  function containsChinese(str: string) {
    return /[\u4e00-\u9fa5]/.test(str);
  }

  function isPureEnglishOrNumber(str: string) {
    return /^[a-zA-Z0-9]+$/.test(str);
  }

  function formatPlayerId(playerId: string) {
    if (!playerId) return "";

    // 如果包含中文
    if (containsChinese(playerId)) {
      // 如果全是中文
      if (isPureEnglishOrNumber(playerId.replace(/[\u4e00-\u9fa5]/g, ""))) {
        if (playerId.length <= 6) return playerId;
        return playerId.slice(0, 3) + "..." + playerId.slice(-3);
      }
      // 中英文混合
      if (playerId.length <= 8) return playerId;
      return playerId.slice(0, 4) + "..." + playerId.slice(-2);
    }

    // 纯英文/数字
    if (playerId.length <= 10) return playerId;
    return playerId.slice(0, 5) + "..." + playerId.slice(-5);
  }

  // 关闭右键菜单
  const closeContextMenu = () => setContextMenu(null);

  // 监听全局点击关闭右键菜单
  useEffect(() => {
    if (!contextMenu) return;
    const handler = () => closeContextMenu();
    window.addEventListener("click", handler);
    window.addEventListener("scroll", handler, true);
    return () => {
      window.removeEventListener("click", handler);
      window.removeEventListener("scroll", handler, true);
    };
  }, [contextMenu]);

  // 右键Reply
  const handleReply = (user: string, message: string) => {
    setReplyInfo({ user, message });
    closeContextMenu();
    // 打开回复面板
    setTimeout(() => {
      replyRef.current?.openReplyMessage(user, message);
    }, 0);
  };

  // 关闭回复面板时
  const handleCloseReply = () => {
    setReplyInfo(null);
    replyRef.current?.closeReplyMessage();
  };

  if (typeList.length === 0) {
    return null;
  }

  return (
    <>
      <ChatRoomView isExpanded={isExpanded}>
        {isExpanded && (
          <ToolBar
            typeList={typeList}
            onChange={(v) => {
              console.log("onChange", v);
              setCurChatType(v);
            }}
            onAdminChange={(v) => {
              console.log("onAdminChange", v);
              setAdminOnly(v);
            }}
          />
        )}

        <ToggleButton
          isExpanded={isExpanded}
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? (
            <ChevronRightIcon />
          ) : (
            <ChevronLeftIconContainer>
              <ChevronLeftIcon />{" "}
              <span
                style={{
                  fontSize: "16px",
                }}
              >
                Show Chat Dialog
              </span>
            </ChevronLeftIconContainer>
          )}
        </ToggleButton>

        {isExpanded && (
          <MessageList ref={messageListRef}>
            {messages.map((message, index) => {
              // 过滤掉空消息
              if (!message.content || trim(message.content) === "") {
                return null;
              }

              // 使用hook获取玩家颜色和特殊账号属性
              const specialAccountProps = getSpecialAccountProps(
                message.playerId
              );
              const playerColor = getPlayerColor(
                message.playerId,
                myPlayer.btcAddress
              );
              const isCurrentPlayer = message.playerId === myPlayer.btcAddress;
              const isContextActive =
                contextMenu && contextMenu.index === index;

              return (
                <MessageItem
                  key={index}
                  isCurrentPlayer={isCurrentPlayer}
                  playerColor={playerColor}
                  style={
                    isContextActive
                      ? { background: "rgba(255,255,255,0.15)" }
                      : {}
                  }
                  onContextMenu={(e) => {
                    e.preventDefault();
                    setContextMenu({ x: e.clientX, y: e.clientY, index });
                  }}
                >
                  <div className="player-id-container">
                    <span className="player-id">
                      {formatPlayerId(message.playerId)}
                      <PlayerLabel
                        playerId={message.playerId}
                        currentPlayerId={myPlayer.btcAddress}
                        specialAccount={specialAccountProps}
                      />
                    </span>
                  </div>

                  {/* 使用CopyToClipboard替代之前的onClick处理 */}
                  <CopyToClipboard
                    text={trim(message.content)}
                    onCopy={() =>
                      handleCopySuccess(trim(message.content), index)
                    }
                  >
                    <div className="message-content-container">
                      <span className="message-content">
                        {trim(message.content)}
                      </span>
                    </div>
                  </CopyToClipboard>

                  {copiedMessageIndex === index && (
                    <div className="copy-success-tip">✅ Copied!</div>
                  )}

                  {/* 右键Reply Tooltips */}
                  {isContextActive && contextMenu && (
                    <ReplyButton
                      contextMenu={contextMenu}
                      message={message}
                      handleReply={handleReply}
                    />
                  )}
                </MessageItem>
              );
            })}
          </MessageList>
        )}
        {/* 新消息提示 */}
        {showNewMessagePrompt && isExpanded && (
          <div
            className="new-message-tip"
            onClick={() => {
              scrollToBottom();
              setShowNewMessagePrompt(false);
            }}
          >
            New Message
          </div>
        )}

        {/* 回复消息 */}
        {isExpanded && (
          <ChatRoomReplyMessage
            ref={replyRef}
            myPlayer={myPlayer}
            handleCloseReply={handleCloseReply}
          />
        )}

        {/* 输入框，出现回复时向下移动 */}
        {isExpanded && (
          <ChatRoomInput
            replyMode={!!replyInfo}
            curChatType={curChatType}
            onAirdrop={() => {
              airdropModalRef.current?.open();
            }}
          />
        )}

        <AirdropModal
          ref={airdropModalRef}
          onClose={() => {
            airdropModalRef.current?.close();
          }}
          onReward={(reward) => {
            openRewards({
              name: reward.name,
              quantity: reward.quantity,
            });
          }}
        />
      </ChatRoomView>
      <Rewards />
    </>
  );
};

export default ChatRoom;
