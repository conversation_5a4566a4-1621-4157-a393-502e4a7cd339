import styled from "styled-components";

export const ChatRoomView = styled.div<{ isExpanded: boolean }>`
  position: relative;
  width: ${(props) => (props.isExpanded ? "570px" : "200px")};
  height: ${(props) => (props.isExpanded ? "280px" : "50px")};
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 20px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  z-index: 1000;
  border: 1px solid #fff;
  .new-message-tip {
    position: absolute;
    bottom: 10px; // 保持固定距离底部10px
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.85);
    color: #ffda0b;
    font-size: 12px;
    line-height: 40px; // 修正行高与高度匹配
    text-align: center; // 文本居中
    border-radius: 18px;
    border: 1px solid #ffda0b;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100; // 确保在消息上方显示
    pointer-events: auto; // 确保可点击
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3); // 添加阴影增强视觉效果
  }
`;

export const ToggleButton = styled.button<{ isExpanded: boolean }>`
  position: absolute;
  top: 0px;
  left: 0px;
  ${(props) =>
    props.isExpanded
      ? `
    width: 100%;
  `
      : `
    width: 100%;
  `}
  background: none; // 移除背景色
  border: none;
  color: white;
  cursor: pointer;
  padding: 0; // 移除内边距
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const MessageList = styled.div`
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden; // 禁用水平滚动
  padding: 30px 20px 20px 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px; /* 滚动条宽度 */
  }

  &::-webkit-scrollbar-track {
    background: transparent; /* 滚动条轨道背景 */
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 1); /* 滚动条颜色，半透明白色 */
    border-radius: 2px; /* 圆角 */
  }

  /* 鼠标悬停时的滚动条样式 */
  &::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 1); /* 悬停时稍微更亮一些 */
  }

  /* 隐藏 Firefox 的默认滚动条 */
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 1) transparent;
`;

export const MessageItem = styled.div<{
  isCurrentPlayer: boolean;
  playerColor: string;
}>`
  display: flex;
  gap: 6px;
  width: 100%; // 确保消息项占满容器宽度
  align-items: start;
  position: relative; // 添加相对定位，用于复制提示动画定位
  cursor: pointer; // 添加指针样式，提示可点击

  .player-id-container {
    flex-shrink: 0; // 防止玩家ID区域被压缩
    min-width: 150px; // 设置固定最小宽度
    max-width: 150px; // 设置固定最大宽度
  }

  .player-id {
    font-size: 14px;
    font-weight: bold;
    color: ${(props) =>
      props.isCurrentPlayer ? "#FFFF27" : props.playerColor};
    white-space: nowrap; // 防止玩家ID换行
    overflow: hidden; // 超出部分隐藏
    text-overflow: ellipsis; // 超出显示省略号
    display: block; // 确保省略号正常工作
    position: relative;
  }

  .message-content-container {
    flex: 1; // 让消息内容区域占据剩余空间
    min-width: 0; // 允许内容区域被压缩
    font-size: 16px;
    line-height: 16px;
    transition: all 0.2s ease;
    padding: 2px 4px;
    border-radius: 4px;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1); // 悬停时的背景色
      text-decoration: underline; // 悬停时添加下划线
      text-decoration-style: dotted; // 下划线样式为点状
      text-decoration-color: rgba(255, 255, 255, 0.5); // 下划线颜色
      text-underline-offset: 3px; // 下划线偏移
    }
  }

  .message-content {
    color: ${(props) =>
      props.isCurrentPlayer ? "#FFFF27" : props.playerColor};
    word-wrap: break-word; // 允许长单词换行
    word-break: break-all; // 确保所有文本都能换行
    white-space: pre-wrap; // 保留空格和换行，同时允许自动换行
    user-select: text; // 允许文本选择
  }

  .copy-success-tip {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none; // 防止动画影响鼠标事件
    animation: fadeOutUp 1.5s forwards;
  }

  @keyframes fadeOutUp {
    0% {
      opacity: 0;
      transform: translate(-50%, 10px);
    }
    20% {
      opacity: 1;
      transform: translate(-50%, 0);
    }
    80% {
      opacity: 1;
      transform: translate(-50%, 0);
    }
    100% {
      opacity: 0;
      transform: translate(-50%, -10px);
    }
  }
`;

export const ChevronLeftIconContainer = styled.div`
  font-size: 30px;
  line-height: 25px;
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
`;