import styled from "styled-components";

export const HomePageView = styled.div`
  height: 100vh;
  background: linear-gradient(0deg, #FF8316, #FF8316);
  overflow-y: auto;
  .home-header{
    background: #FF8316;
    height: 104px;
    width: calc(100% - 24px);
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    padding-right: 20px;
    box-sizing: border-box;
    //clip-path: inset(0 20px 0 0);
  }
  .home-page{
    width: 100%;
    height: 100%;
    padding: 0 20px;
    box-sizing: border-box;
    .home-page-bg{
      background: radial-gradient(39.62% 39.62% at 50% 100%, rgba(233, 19, 0, 0.5) 0%, rgba(255, 131, 22, 0.5) 100%);
      width: 100%;
      height: 100vh;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 0;
      
    }
    .home-page-content{
      width: 100%;
      max-width: 1600px;
      margin: 0 auto;
      padding-top: calc(136px + 190px);
      position: relative;
      z-index: 0;
    }
  }
`
