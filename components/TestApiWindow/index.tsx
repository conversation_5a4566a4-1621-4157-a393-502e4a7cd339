import React, {use<PERSON><PERSON>back, useMemo, useRef, useState} from "react";
import {TestApiWindowView, ToggleButton} from "./style";
import {ButlerUtil} from "@/world/Global/GlobalButlerUtil";
import GlobalSpaceEvent, {GlobalDataKey,} from "../../world/Global/GlobalSpaceEvent";
import * as THREE from "three";
import PetManager from "@/world/Pet/PetManager";
import {useTwitterAuth} from "@/hooks/useTwitterAuth";
import {useDispatch, useSelector} from "react-redux";
import {IAppState, IGameState} from "@/constant/type";
import toast from "react-hot-toast";
import {
  setEasterEggInfo,
  setFishEasterEggModal,
  setLeaderboard,
  setLeftTime,
  setRandomEventResult,
  setUserBasicInfo,
} from "@/store/app";
import {AppGameApiKey, GetMyPlayer} from "@/world/Character/MyPlayer";
import {useComboManager} from "../Combo/ComboManager";
import useRewards from "@/components/Rewards";
import useHandItem from "@/hooks/useHandItem";
import {useFish} from "@/hooks/useFish";
import {getMaterialList, getUserDropItemList, submitMaterial,} from "@/server";
import {Events} from "@/utils/clientEvents";
import {setBagInventoryList} from "@/store/game";
import {ItemConfig, ItemType} from "@/world/Config/ItemConfig";
import {createParams, rsaEncrypt} from "@/utils";
import CommunityTableModal, {CommunityTableModalRef} from "../CommunityTable";
import {ResourceTableModal, ResourceTableModalRef} from "../ResourceTable";
import Claim, {ClaimRef} from "../Claim";
import Submission, {SubmissionRef} from "../Submission";
import confetti from "canvas-confetti";
import {GetGameNetWork} from "@/world/hooks/useNetWork";
import {KeyPressUtil} from "@/world/Global/GlobalKeyPressUtil";
import {ItemDropConfig} from "@/world/Config/ItemDropConfig";
import AvatarData from "@/AvatarOrdinalsBrowser/renderAvatar/Avatar/Data/AvatarData";
import {generateUUID} from "three/src/math/MathUtils";
import {getPizzaActivity} from "@/world/Activity/PizzaActivity";
import createAnimate from "@/utils/createAnimate";

// 示例字幕数据
const SAMPLE_SUBTITLES = [
  {
    start: 0,
    end: 3.8,
    content: "文件是一种常用于web视频字幕的文本格式。",
  },
  {
    start: 3.8,
    end: 7.6,
    content: "它的基本结构由两部分组成,时间戳和字幕文本。",
  },
  {
    start: 7.6,
    end: 13.6,
    content:
      "每个字幕段落由一个时间戳、字幕文本以及可能的其他设置如样式、位置等构成。",
  },
  {
    start: 13.6,
    end: 16.8,
    content: "以下是一个VTT文件的典型结构和实例。",
  },
];

// 示例音频URL
const SAMPLE_AUDIO_URL =
  "https://static.satworld.io/test_webm/c510a03bf1b943c0e28ec439b24e0d9f_zh_main.wav";

// 示例引用文件
const SAMPLE_CITATION_FILES = [
  {
    fileId: "file-7rm5ySmMX4F6itwbPjZAcH",
    fileUrl: "https://static.satworld.io/openai/1739521691993_temp.pdf",
    title: "深夜手机失去控制 信用卡被刷爆！警方提醒：这一功能赶紧关闭.pdf",
    webLink: "https://baijiahao.baidu.com/s?id=1823991163793395911",
  },
  {
    fileId: "file-7rm5ySmMX4F6itwbPjZAcH",
    fileUrl: "https://static.satworld.io/openai/1739521691993_temp.pdf",
    title: "深夜手机失去控制 信用卡被刷爆！警方提醒：这一功能赶紧关闭.pdf",
    webLink: "",
  },
];

// 定义API类型
interface ApiAction {
  name: string;
  call: () => void;
  category?: string;
  loading?: boolean;
  disabled?: boolean;
}

const TestApiWindow: React.FC = () => {
  const myPlayer = GetMyPlayer();
  const gameNetWork = GetGameNetWork();
  // 添加显示/隐藏状态
  const [isVisible, setIsVisible] = useState<boolean>(true);
  const dispatch = useDispatch();
  // 添加按钮加载状态
  const [twitterLoading, setTwitterLoading] = useState(false);
  const [equipmentLoading, setEquipmentLoading] = useState(false);
  const [scoreLoading, setScoreLoading] = useState(false);
  const {showComboWithType, ComboDisplay} = useComboManager();
  const comboNumberRef = useRef(0);
  const {reportScorePickAxe, onReceiveTool} = useHandItem();
  const {onCompleteFishEggTask} = useFish();
  const resourceTableModalRef = useRef<ResourceTableModalRef>(null);
  const claimRef = useRef<ClaimRef>(null);
  const submissionRef = useRef<SubmissionRef>(null);
  const [showFailed, setShowFailed] = useState(false);
  const {isOpen, setIsOpen, Rewards} = useRewards();
  const {btcAddress, axeParams, treeList, userBasicInfo, btcWallet} =
    useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const {bagInventoryList} = useSelector(
    (state: { GameReducer: IGameState }) => state.GameReducer
  );
  const {startAuth, isLoading} = useTwitterAuth({
    onSuccess: (data) => {
      setTwitterLoading(false);
    },
    onError: (error) => {
      setTwitterLoading(false);
    },
  });
  const communityTableModalRef = useRef<CommunityTableModalRef>(null);
  // 切换显示/隐藏状态
  const toggleVisibility = () => {
    setIsVisible((prev) => !prev);
  };

  // 处理Twitter认证
  const handleTwitterAuth = useCallback(async () => {
    try {
      await startAuth();
    } catch (error) {
      console.error("Twitter auth error:", error);
    }
  }, [startAuth]);

  // 检查斧头耐久度是否为0
  const isAxeDurabilityZero = useMemo(() => {
    // 如果存在userItemId，但是totalDurability为0，则表示当前斧头耐久度为0，需要重新领取斧头，否则表示斧头使用中
    return !!axeParams?.userItemId && axeParams.currentDurability === 0;
  }, [axeParams]);

  // 处理领取装备
  const handleReceiveEquipment = useCallback(async () => {
    // 直接调用领取斧头的逻辑
    myPlayer.callAppApi(AppGameApiKey.receiveAxe);
  }, [myPlayer]);

  const startCooling = () => {
    dispatch(setLeftTime(10));
  };

  const startRule = () => {
    myPlayer.callAppApi(AppGameApiKey.activityRule);
  };

  const startCombo = (combo: number) => {
    comboNumberRef.current += combo;
    showComboWithType(comboNumberRef.current);
  };

  // useEffect(() => {
  //   if (dogEasterEgg && dogEasterEgg.length) {
  //     animationSequenceRef.current?.start();
  //   }
  // }, [dogEasterEgg]);

  // 使用 useMemo 缓存 API 列表
  const apiList = useMemo(() => {
    // 获取宠物对象的辅助函数
    const getPetObject = (object: THREE.Object3D): THREE.Object3D => {
      const pet = object.userData.pet as THREE.Object3D;
      if (pet) {
        return getPetObject(pet);
      }
      return object;
    };
    return [
      {
        name: "彩带飘落",
        call: async () => {
          // createAnimate();
          createAnimate({
            emojis: ["🍕"],
            duration: 8000,
          });
        },
        category: "pizza活动",
        loading: false,
        disabled: false,
      },
      {
        name: "重置活动",
        call: async () => {
          const pizzaActivity = getPizzaActivity()
          pizzaActivity.updateActivityData(true)
        },
        category: "pizza活动",
        loading: false,
        disabled: false,
      },
      {
        name: "测试报名",
        call: async () => {
          const pizzaActivity = getPizzaActivity()
          pizzaActivity.joinActivity(true)
        },
        category: "pizza活动",
        loading: false,
        disabled: false,
      },
      {
        name: "检查活动",
        call: async () => {
          const pizzaActivity = getPizzaActivity()
          const activityData = pizzaActivity.getActivityData()
          const now = Date.now()
          if (now < activityData.startTime) {
            toast.success('游戏开始还有' + (Math.floor((activityData.startTime - now) / 1000)) + '秒')
            return
          }
        },
        category: "pizza活动",
        loading: false,
        disabled: false,
      },
      {
        name: "显示所有点",
        call: async () => {
          const pizzaActivity = getPizzaActivity()
          pizzaActivity.showAllPoint()
        },
        category: "pizza活动",
        loading: false,
        disabled: false,
      },
      {
        name: "获取掉落物列表",
        call: async () => {
          interface IUserDropItem {
            dropItemTag: string; // 掉落物品Tag
            isPickedUp: boolean; // 是否已拾取
            tag: string; // 掉落位置tag
            quantity: number; // 掉落数量
          }

          const list = await getUserDropItemList();
          const {code, msg, data} = list.data;
          if (code === 1) {
            const dropItemList = data as IUserDropItem[];
            // console.log("dropItemList=====", dropItemList);
          } else {
            console.error(msg);
          }
        },
        category: "拾取资源",
        loading: false,
        disabled: false,
      },
      {
        name: "检查掉落物刷新",
        call: () => {
          const time = ItemDropConfig.getInstance().getLastRefreshTime() - Date.now();
          toast.success("刷新还需要等待=====" + Math.floor(time / 1000) + "秒");
        },
        category: "拾取资源",
        loading: false,
        disabled: false,
      },
      {
        name: "获取房间列表",
        call: async () => {
          const list = await gameNetWork.getRoomList();
          console.log("gameNetWork.getRoomList()", list);
        },
        category: "房间相关",
        loading: false,
        disabled: false,
      },
      {
        name: "进入房间1",
        call: () => {
          gameNetWork.enterRoom(2, 1);
        },
        category: "房间相关",
        loading: false,
        disabled: false,
      },
      {
        name: "进入房间2",
        call: () => {
          gameNetWork.enterRoom(2, 2);
        },
        category: "房间相关",
        loading: false,
        disabled: false,
      },
      {
        name: "进入房间3",
        call: () => {
          gameNetWork.enterRoom(2, 3);
        },
        category: "房间相关",
        loading: false,
        disabled: false,
      },
      {
        name: "合成背包",
        call: () => {
          myPlayer.callAppApi(AppGameApiKey.openSynthesis);
        },
        category: "背包",
        loading: false,
        disabled: false,
      },
      {
        name: "打开Rewards",
        call: () => {
          setIsOpen(true);
        },
        category: "社区",
        loading: false,
        disabled: false,
      },
      {
        name: "3D资源排行榜表格",
        call: () => {
          resourceTableModalRef.current?.open();
        },
        category: "社区",
        loading: false,
        disabled: false,
      },
      {
        name: "空投彩蛋",
        call: () => {
          dispatch(
            setRandomEventResult({
              quantity: 0.01,
            })
          );
        },
        category: "社区",
        loading: false,
        disabled: false,
      },
      {
        name: "提交资源到社区",
        call: () => {
          confetti({
            particleCount: 100,
            spread: 100,
            origin: {x: 0.4, y: 0.5},
            zIndex: 2000,
            ticks: 200,
          });

          setTimeout(() => {
            // 延迟后触发第二个粒子效果
            confetti({
              particleCount: 100,
              spread: 100,
              origin: {x: 0.6, y: 0.8},
              zIndex: 2000,
              ticks: 200,
            });
          }, 300); // 300毫秒的延迟，可以根据需要调整

          setTimeout(() => {
            submissionRef.current?.open(
              {
                wood: 100,
                stone: 100,
                fish: 100,
              },
              "potato"
            );
          }, 500);
        },
        category: "社区",
        loading: false,
        disabled: false,
      },
      {
        name: "社区表格",
        call: async () => {
          communityTableModalRef.current?.open();
        },
        category: "社区",
        loading: false,
        disabled: false,
      },
      {
        name: "上交材料(potato)",
        call: async () => {
          const params = createParams(btcAddress, `/activity-rank/cmi`);
          const encrypted = rsaEncrypt(params);
          const res = await submitMaterial(
            {tick: "potato"},
            {sw: encrypted}
          );
          const {code, msg, data} = res.data;
          if (code === 1) {
            dispatch(setLeaderboard(data));
          } else {
            console.error(msg);
          }
        },
        category: "社区",
        loading: false,
        disabled: false,
      },
      {
        name: "领取空投",
        call: () => {
          claimRef.current?.open(
            {
              faucetAmount: "10.234",
              faucetType: "potato",
              tickBalance: "103.234",
              tickIcon: "/image/potato.png",
            },
            "potato"
          );
        },
        category: "社区",
        loading: false,
        disabled: false,
      },
      {
        name: "上交材料(wangcai)",
        call: async () => {
          const params = createParams(btcAddress, "/activity-rank/cmi");
          const encrypted = rsaEncrypt(params);
          const res = await submitMaterial(
            {tick: "wangcai"},
            {sw: encrypted}
          );
          const {code, msg, data} = res.data;
          if (code === 1) {
            dispatch(setLeaderboard(data));
          } else {
            console.error(msg);
          }
        },
        category: "社区",
        loading: false,
        disabled: false,
      },
      {
        name: "上交材料(thelonelybit)",
        call: async () => {
          const params = createParams(btcAddress, "/activity-rank/cmi");
          const encrypted = rsaEncrypt(params);
          const res = await submitMaterial(
            {tick: "TheLonelyBit"},
            {sw: encrypted}
          );
          const {code, msg, data} = res.data;
          if (code === 1) {
            dispatch(setLeaderboard(data));
          } else {
            console.error(msg);
          }
        },
        category: "社区",
        loading: false,
        disabled: false,
      },
      {
        name: "获取材料列表",
        call: async () => {
          const res = await getMaterialList();
          const {code, msg, data} = res.data;
          if (code === 1) {
          } else {
            console.error(msg);
          }
        },
        category: "背包",
        loading: false,
        disabled: false,
      },
      {
        name: "切换下一把斧头",
        call: () => {
          ItemConfig.getInstance().testHandItem(
            ItemType.Axe,
            bagInventoryList,
            (newList) => {
              dispatch(setBagInventoryList(newList));
            }
          );
        },
        category: "背包",
        loading: false,
        disabled: false,
      },
      {
        name: "切换下一把稿子",
        call: () => {
          ItemConfig.getInstance().testHandItem(
            ItemType.Pickaxe,
            bagInventoryList,
            (newList) => {
              dispatch(setBagInventoryList(newList));
            }
          );
        },
        category: "背包",
        loading: false,
        disabled: false,
      },
      {
        name: "切换下一把鱼竿",
        call: () => {
          ItemConfig.getInstance().testHandItem(
            ItemType.FishingRod,
            bagInventoryList,
            (newList) => {
              dispatch(setBagInventoryList(newList));
            }
          );
        },
        category: "背包",
        loading: false,
        disabled: false,
      },
      {
        name: "采集钓鱼材料动画",
        call: () => {
          Events.emitItemCollected("/image/yu.png", 1);
        },
        category: "背包",
        loading: false,
        disabled: false,
      },
      {
        name: "采集木头动画",
        call: () => {
          Events.emitItemCollected("/image/t2-1.png", 1);
        },
        category: "背包",
        loading: false,
        disabled: false,
      },
      {
        name: "采集矿石动画",
        call: () => {
          Events.emitItemCollected("/image/t2-2.png", 1);
        },
        category: "背包",
        loading: false,
        disabled: false,
      },
      {
        name: "领取鱼竿",
        call: async () => {
          // myPlayer.callAppApi(AppGameApiKey.receiveFishingRod);
          onReceiveTool("123", "FishingPole").then();
        },
        category: "钓鱼活动",
        loading: false,
        disabled: false,
      },
      {
        name: "甩竿",
        call: async () => {
          myPlayer.callAppApi(AppGameApiKey.useFishingRod);
        },
        category: "钓鱼活动",
        loading: false,
        disabled: false,
      },

      {
        name: "上报积分",
        call: () => {
          myPlayer.callAppApi(AppGameApiKey.fishingSuccess);
        },
        category: "钓鱼活动",
        loading: false,
        disabled: false,
      },
      {
        name: "钓鱼彩蛋入口",
        call: () => {
          dispatch(
            setEasterEggInfo([
              {
                domainName: "3.uniworlds",
                isSuccess: true,
              },
              {
                domainName: "1.uniworlds",
                isSuccess: true,
              },
              {
                domainName: "38548.uniworlds",
                isSuccess: true,
              },
              {
                domainName: "2.uniworlds",
                isSuccess: false,
              },
              {
                domainName: "4.uniworlds",
                isSuccess: false,
              },
            ])
          );
        },
        category: "钓鱼活动",
        loading: false,
        disabled: false,
      },
      {
        name: "完成彩蛋任务",
        call: () => {
          onCompleteFishEggTask();
        },
        category: "钓鱼活动",
        loading: false,
        disabled: false,
      },
      {
        name: "钓鱼彩蛋进度弹窗",
        call: () => {
          dispatch(setFishEasterEggModal(true));
        },
        category: "钓鱼活动",
        loading: false,
        disabled: false,
      },
      {
        name: isAxeDurabilityZero
          ? "领取新稿子"
          : axeParams?.userItemId
            ? "稿子使用中"
            : "领取稿子",
        call: async () => {
          // myPlayer.callAppApi(AppGameApiKey.receiveFishingRod);
          onReceiveTool("123", "Pickaxe").then();
        },
        category: "挖矿活动",
        loading: equipmentLoading,
      },
      {
        name: "打开失败动画",
        call: () => {
          setShowFailed(true);
        },
        category: "挖矿活动",
        loading: false,
        disabled: false,
      },
      {
        name: "打开胜利动画",
        call: () => {
          // setShowVictory(true);
        },
        category: "挖矿活动",
        loading: false,
        disabled: false,
      },
      {
        name: "狗头彩蛋弹窗",
        call: () => {
          myPlayer.callAppApi(AppGameApiKey.startDogEgg);
        },
        category: "挖矿活动",
        loading: false,
        disabled: false,
      },
      {
        name: "推特认证",
        call: handleTwitterAuth,
        category: "砍树活动",
        loading: twitterLoading,
        disabled: false,
      },
      {
        name: isAxeDurabilityZero
          ? "领取新斧头"
          : axeParams?.userItemId
            ? "斧头使用中"
            : "领取斧头",
        call: handleReceiveEquipment,
        category: "砍树活动",
        loading: equipmentLoading,
        // disabled: axeParams?.userItemId && axeParams?.totalDurability > 0, // 如果有斧头且耐久度大于0，则禁用按钮
      },
      {
        name: "冷却期50s",
        call: startCooling,
        category: "砍树活动",
        loading: false,
        disabled: false,
      },
      {
        name: "活动规则说明",
        call: startRule,
        category: "砍树活动",
        loading: false,
        disabled: false,
      },
      {
        name: "Combo连击",
        call: () => startCombo(1),
        category: "砍树活动",
        loading: false,
        disabled: false,
      },
      {
        name: "领取结算",
        call: () => {
          const name = 'potato'
          const quantity = 100
          myPlayer.callAppApi(AppGameApiKey.showRewards, name, quantity);
        },
        category: "砍树活动",
        loading: false,
        disabled: false,
      },
      {
        name: "测试斧子上限刷新(5s)",
        call: () => {
          if (userBasicInfo) {
            const ttl = 5;
            myPlayer.refreshTimeStamp = 0;
            setTimeout(() => {
              dispatch(
                setUserBasicInfo({
                  ...userBasicInfo,
                })
              );
            });
          }
        },
        category: "砍树活动",
        loading: false,
        disabled: false,
      },
      // 播报相关API
      {
        name: "更新播报链接",
        call: () => {
          ButlerUtil.updateBroadcast(SAMPLE_SUBTITLES, SAMPLE_AUDIO_URL, true);
        },
        category: "播报",
        loading: false,
        disabled: false,
      },

      // 管家控制相关API
      {
        name: "更新管家数据",
        call: () => ButlerUtil.updateButlerTransform(),
        category: "管家",
        loading: false,
        disabled: false,
      },
      {
        name: "隐藏管家",
        call: () => ButlerUtil.hideButler(),
        category: "管家",
        loading: false,
        disabled: false,
      },
      {
        name: "显示访客出身点",
        call: () => ButlerUtil.updateVisitorTransform(true),
        category: "管家",
        loading: false,
        disabled: false,
      },

      // 玩家控制相关API
      {
        name: "开启玩家控制",
        call: () => {
          KeyPressUtil.setEnable(true);
        },
        category: "玩家",
        loading: false,
        disabled: false,
      },
      {
        name: "禁止玩家控制",
        call: () => {
          KeyPressUtil.setEnable(false);
        },
        category: "玩家",
        loading: false,
        disabled: false,
      },
      {
        name: "测试斧子消失",
        call: () => {
          if (myPlayer.axeParams) {
            myPlayer.axeParams.currentDurability = 0
          }
        },
        category: "玩家",
        loading: false,
        disabled: false,
      },
      {
        name: "打印玩家坐标",
        call: () => {
          console.log(myPlayer.position);
          toast.success(
            myPlayer.position.x.toFixed(2) +
            "," +
            myPlayer.position.y.toFixed(2) +
            "," +
            myPlayer.position.z.toFixed(2)
          );
        },
        category: "玩家",
        loading: false,
        disabled: false,
      },
      {
        name: "增加pizza",
        call: () => {
          myPlayer.addPizza()
        },
        category: "玩家",
        loading: false,
        disabled: false,
      },
      {
        name: "清空pizza",
        call: () => {
          myPlayer.removePizza()
        },
        category: "玩家",
        loading: false,
        disabled: false,
      },
      {
        name: "创建连接玩家",
        call: () => {
          if (gameNetWork.isConnected()) {
            const btcAddress = generateUUID()
            const otherPlayer = gameNetWork.addOtherPlayer(btcAddress, new AvatarData())
            setTimeout(() => {
              otherPlayer.position.copy(myPlayer.position)
              otherPlayer.quaternion.copy(myPlayer.quaternion)
              otherPlayer.isChange = true
            }, 1000)
          }
        },
        category: "玩家",
        loading: false,
        disabled: false,
      },
      {
        name: "开启自由视角",
        call: () => {
          GlobalSpaceEvent.SetDataValue<boolean>(
            GlobalDataKey.OpenFreeCamera,
            true
          );
        },
        category: "视角",
        loading: false,
        disabled: false,
      },
      {
        name: "关闭自由视角",
        call: () => {
          GlobalSpaceEvent.SetDataValue<boolean>(
            GlobalDataKey.OpenFreeCamera,
            false
          );
        },
        category: "视角",
        loading: false,
        disabled: false,
      },

      // 宠物相关API
      {
        name: "创建一个宠物",
        call: () => {
          const player = ButlerUtil.getOtherPlayerObj();
          if (player) {
            PetManager.addPet(getPetObject(player), 0);
          }
        },
        category: "宠物",
        loading: false,
        disabled: false,
      },
      {
        name: "清空宠物",
        call: () => {
          const player = ButlerUtil.getOtherPlayerObj();
          if (player) {
            player.userData.pet = null;
            PetManager.clearPet();
          }
        },
        category: "宠物",
        loading: false,
        disabled: false,
      },
    ];
  }, [
    handleTwitterAuth,
    twitterLoading,
    isLoading,
    handleReceiveEquipment,
    equipmentLoading,
    scoreLoading,
    axeParams,
    btcAddress,
    treeList,
    isAxeDurabilityZero,
  ]);

  // 按类别分组API
  const groupedApis = useMemo(() => {
    const groups: Record<string, ApiAction[]> = {};

    apiList.forEach((api: any) => {
      const category = api.category || "其他";
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(api);
    });

    return groups;
  }, [apiList]);

  return (
    <>
      {/* 显示/隐藏开关按钮 */}
      <ToggleButton onClick={toggleVisibility} isVisible={isVisible}>
        {isVisible ? "隐藏工具" : "显示工具"}
      </ToggleButton>

      {/* API窗口 */}
      {isVisible && (
        <TestApiWindowView>
          <div className="window-header">
            <h2>API测试工具</h2>
            <button className="close-button" onClick={toggleVisibility}>
              ×
            </button>
          </div>

          {Object.entries(groupedApis).map(([category, apis]) => (
            <div key={category} className="api-category">
              <h3>{category}</h3>
              <div className="api-buttons">
                {apis.map((api, index) => (
                  <button
                    key={`${category}-${index}`}
                    onClick={api.call}
                    className={`api-button ${api.loading ? "loading" : ""}`}
                    disabled={api.loading || api.disabled}
                  >
                    {api.loading ? `${api.name}中...` : api.name}
                  </button>
                ))}
              </div>
            </div>
          ))}
        </TestApiWindowView>
      )}
      {ComboDisplay && <ComboDisplay/>}
      <ResourceTableModal
        ref={resourceTableModalRef}
        onClose={() => {
        }}
        resourceType="stone"
      />
      <Claim ref={claimRef} onClose={() => {
      }}/>
      <Submission ref={submissionRef} onClose={() => {
      }}/>
      <CommunityTableModal ref={communityTableModalRef} onClose={() => {
      }}/>
      {
        isOpen && (
          <Rewards/>
        )
      }
    </>
  );
};

export default TestApiWindow;
