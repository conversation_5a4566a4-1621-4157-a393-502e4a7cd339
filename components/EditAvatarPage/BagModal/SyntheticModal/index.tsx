import {NumStepView, SyntheticModalView} from "./style";
import Modal from "../../BasicComponents/Modal";
import SyntheticImg from '/public/image/bag/synthetic.png'
import CloseImg from '/public/image/bag/close.svg'
import ContentItem from "../ContentItem";
import {useMemo, useState} from "react";
import {getSyntheticsList, mergeSynthesize} from "../../../../server";
import {useSelector} from "react-redux";
import {IAppState, IBagInventoryItem, ISyntheticItem} from "../../../../constant/type";
import toast from "react-hot-toast";
import useBagInventory from "../../../../hooks/useBagInventory";

export default function SyntheticModal({syntheticItem, onClose, inventoryMap}: {
  syntheticItem: ISyntheticItem | null, onClose: Function, inventoryMap: {
    [key: string]: IBagInventoryItem
  }
}) {
  const {getBagInventoryListDta, getSyntheticsListData} = useBagInventory()

  const [currentNum, setCurrentNum] = useState<number>(1)
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false)

  if (syntheticItem === null) {
    return null
  }

  const onSynthesize = () => {
    setConfirmLoading(true)
    const loadingToast = toast.loading('Synthesizing')
    mergeSynthesize({
      count: currentNum,
      itemId: syntheticItem.itemId
    }).then(async res => {
      if (res.data.code === 1) {
        await getBagInventoryListDta()
        await getSyntheticsListData()
        toast.success("Synthesize Success")
      } else {
        toast.error(res.data.msg || res.data.message[0], {duration: 6000})
      }
    }).finally(() => {
      toast.dismiss(loadingToast);
      setConfirmLoading(false)
    })
  }
  return <Modal visible={true} zIndex={10} emptyOnly={true}>
    <SyntheticModalView>
      <img src={SyntheticImg.src} alt="" className="synthetic-title"/>
      <img src={CloseImg.src} alt="" className={"close-btn"} onClick={() => onClose()}/>
      <div className="synthetic-content">
        <div className="result-item">
          <ContentItem
            src={syntheticItem.icon}
            num={inventoryMap[syntheticItem.itemId]?.quantity || 0}
            redMark={syntheticItem.canSynthesize > 0}
            boxShadow={"0px 0px 21.18px 0px #FF831680"}
          />
          <p>{syntheticItem.name}</p>
        </div>
        <div className="depletion-list">
          {
            syntheticItem.synthetics.map(item => {
              const quantity = item.currentQuantity
              const need = item.needQuantity * currentNum
              return <div className="depletion-item" key={item.itemId}>
                <ContentItem
                  src={item.icon}
                  boxShadow={"0px 2px 8px 0px #00000026;"}
                />
                <p className={need > quantity ? "not-enough" : ''}>{quantity}/{need}</p>
              </div>
            })
          }
        </div>
        <div className="num-view">
          <NumStep max={syntheticItem.canSynthesize} currentNum={currentNum} setCurrentNum={setCurrentNum}/>
        </div>
        <button className={"confirm-btn"}
                disabled={currentNum > syntheticItem.canSynthesize || currentNum <= 0 || confirmLoading}
                onClick={onSynthesize}>
          <span>Confirm</span>
        </button>
      </div>
    </SyntheticModalView>
  </Modal>
}

function NumStep({max, currentNum, setCurrentNum}: { max: number, currentNum: number, setCurrentNum: Function }) {
  return <NumStepView>
    <div className={currentNum <= 1 ? 'disabled' : ''} onClick={() => {
      if (currentNum > 1) {
        setCurrentNum(currentNum - 1)
      }
    }}>-
    </div>
    <span>{currentNum}/{max}</span>
    <div className={currentNum >= max ? 'disabled' : ''} onClick={() => {
      if (currentNum < max) {
        setCurrentNum(currentNum + 1)
      }
    }}>+
    </div>
  </NumStepView>
}
