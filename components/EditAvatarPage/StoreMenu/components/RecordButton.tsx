import {IAppState, SCENE_TYPE, STORAGE_MENU_ENUM} from "@/constant/type";
import {setStorageMenu} from "@/store/app";
import {forwardRef, Ref, useEffect, useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import ReloadingMenuSvg from "/public/image/storage-menu/recording-menu.svg";
// import ReloadingMenuHoverSvg from "/public/image/storage-menu/recording-menu-hover.svg";
import {DOMAIN_JUMP} from "@/constant";

interface RecordButtonProps {}

interface RecordButtonRef {}

const RecordButton = forwardRef(
  (props: RecordButtonProps, ref: Ref<RecordButtonRef>) => {
    const {
      storageMenu,
      domainOwner,
      isRecording,
      btcAddress,
      isTtsWhiteList,
      sceneType
    } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
    const dispatch = useDispatch();
    const [isUserDomain, setIsUserDomain] = useState<boolean>(false);

    /**
     * 判断是否是"localhost" | ".uniworlds"域名
     * 判断是否是白名单
     * 判断是否在社区，如果在社区则不显示 sceneType SCENE_TYPE.Community比较
     */
    useEffect(() => {
      // 判断是否用户的域名
      const isDomain = window.location.href.includes(DOMAIN_JUMP);
      const isLocalhost = window.location.href.includes("http://localhost");
      const isTtsWhiteList = window.localStorage.getItem("isTtsWhiteList");
      if (isDomain || isLocalhost || isTtsWhiteList) {
        setIsUserDomain(true);
      }
    }, []);

    if (!isUserDomain || sceneType === SCENE_TYPE.Community) {
      return null;
    }

    return (
      <div
        className={
          storageMenu === STORAGE_MENU_ENUM.RECORDING_MENU
            ? "storage-menu-item active"
            : "storage-menu-item"
        }
        onClick={() => {
          dispatch(
            setStorageMenu(
              storageMenu === STORAGE_MENU_ENUM.RECORDING_MENU
                ? null
                : STORAGE_MENU_ENUM.RECORDING_MENU
            )
          );
        }}
      >
        <div className="icon-container">
          <img
            src={ReloadingMenuSvg.src}
            className="default-icon"
            alt="Recording Menu"
          />
        </div>
      </div>
    );
  }
);

export default RecordButton;
